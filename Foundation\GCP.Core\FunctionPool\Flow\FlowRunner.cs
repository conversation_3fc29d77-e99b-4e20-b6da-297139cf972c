using GCP.Common;
using GCP.DataAccess;
using GCP.FunctionPool.Flow;
using GCP.FunctionPool.Flow.Models;
using Jint;
using Serilog;
using System.Collections;
using System.Diagnostics;
using static LinqToDB.Reflection.Methods.LinqToDB.Insert;

namespace GCP.FunctionPool
{
    internal class FlowExecutor
    {
        private FunctionFlow Flow { get; set; }
        private Dictionary<string, FlowStep> ActionDic { get; set; }
        private Dictionary<string, FunctionMiddlewareDelegate[]> ActionMiddlewareDic { get; set; }


        public FlowExecutor() { }
        public FlowExecutor(FunctionFlow flow) { Load(flow); }
        public FlowExecutor(string funcFlowJson) { LoadJson(funcFlowJson); }

        private void LoadJson(string funcFlowJson)
        {
            Load(JsonHelper.Deserialize<FunctionFlow>(funcFlowJson));
        }

        public void Load(FunctionFlow flow)
        {
            if (flow.Body == null || flow.Body.Count == 0)
            {
                Log.Error("未找到对应流程执行内容：{FlowId} {FlowVersion}", flow.Id, flow.Version);
            }
            this.Flow = flow;
            if (flow.Body != null)
            {
                this.ActionDic = flow.Body.ToDictionary(t => t.Id, t => t);
                ActionMiddlewareDic = flow.Body.ToDictionary(t => t.Id,
                    t => t.Middleware is { Count: > 0 }
                        ? t.Middleware.Select(FunctionRunner.GetMiddlewareProxy).ToArray()
                        : ( []));
            }
        }

        /// <summary>
        /// 执行流程
        /// </summary>
        public async Task<object> Run(FunctionContext context)
        {
            context.trackId ??= TUID.NewTUID().ToString();

            if (Flow.Middleware is { Count: > 0 })
            {
                context.Middlewares.AddRange(Flow.Middleware.Select(FunctionRunner.GetMiddlewareProxy).ToArray());
            }

            // 使用懒加载引擎，只在实际需要时创建
            Engine engine = null;

            if (Flow.Data is { Count: > 0 })
            {
                // 检查是否需要引擎，如果需要才创建
                if (FlowUtils.LazyEngineWrapper.RequiresEngine(Flow.Data))
                {
                    engine = FlowUtils.GetEngine(context);
                }
                FlowUtils.BindResult(Flow.Data, context.globalData, engine, context, null, new Dictionary<string, object>(context.Args));
            }
            else
            {
                Flow.Data = [];
            }

            context.SeqNo = 0;
            context.Current.Level = 0;
            context.Current.SeqNo = 0;
            context.Current.IsFlow = true;
            context.Current.StepName = context.Current.FunctionName;

            var root = Flow.Body.FirstOrDefault();
            if (root != null)
            {
                await RunAction(root.Id, engine, context).ConfigureAwait(false);
            }

            context.Result = context.Current.Result ?? context.LastData?.Result;
            return context.Result;
        }

        FunctionProvider CreateProvider(FunctionContext context, FlowStep step, FunctionProvider previousProvider, bool isSibling = false)
        {
            var current = new FunctionProvider();

            if (previousProvider != null)
            {
                context.SeqNo++;
                current.SeqNo = context.SeqNo;
                var provider = previousProvider;
                if (isSibling)
                {
                    provider = provider.Parent ?? previousProvider;
                }

                current.Level = provider.Level + 1;
                current.Parent = provider;
                provider.Children.Add(current);
            }
            current.ActionId = step.Id;
            current.StepName = step.Name;
            current.Args = step.Args;
            current.IsFlow = true;

            context.LastData = current;
            return current;
        }

        void SetMiddleware(FunctionContext context, FlowStep step, List<FunctionMiddlewareDelegate> middlewares)
        {
            var previousProvider = context.Current;

            context.Current = CreateProvider(context, step, previousProvider, true);

            middlewares.Add(async (ctx, next) =>
            {
                await next();

                if (previousProvider != null)
                    context.Current = previousProvider;
            });
        }

        /// <summary>
        /// 递归运行每个步骤
        /// </summary>
        async Task RunAction(string actionId, Engine engine, FunctionContext context)
        {
            if (string.IsNullOrEmpty(actionId)) return;
            if (!ActionDic.TryGetValue(actionId, out var step)) return;
            var middlewares = ActionMiddlewareDic[step.Id].ToList();

            SetMiddleware(context, step, middlewares);

            // 执行当前步骤
            var task = new FunctionRunner().Execute(step.Function, context, [.. middlewares])
                .ContinueWith(async (resultTask) =>
                {
                    var stepResultDic = new Dictionary<string, object>();
                    context.globalData[step.Id] = stepResultDic;

                    var result = await resultTask.ConfigureAwait(false);

                    // 出参 - 使用统一的ROOT节点输出处理
                    if (step.Result != null)
                    {
                        // 懒加载：只在需要时创建引擎
                        var currentEngine = engine;
                        if (currentEngine == null && FlowUtils.LazyEngineWrapper.RequiresEngine(step.Result))
                        {
                            currentEngine = FlowUtils.GetEngine(context);
                        }
                        context.Current.Result = FlowUtils.BindResult(step.Result, stepResultDic, currentEngine, context, result);

                        if (context.Persistence)
                            _ = context.SqlLog.AddFunctionOutputVariable(context.Current.Result);
                    }
                    else if (result is not IAsyncEnumerable<List<object>>)
                    {
                        if (context.Persistence)
                            _ = context.SqlLog.AddFunctionOutputVariable(result);
                    }

                    // 控制条件
                    if (step.ControlType != null && step.Control != null)
                    {
                        var oldBreakToken = context.BreakCancellationTokenSource;
                        var oldContinueToken = context.ContinueCancellationTokenSource;
                        context.BreakCancellationTokenSource = new CancellationTokenSource();
                        context.ContinueCancellationTokenSource = new CancellationTokenSource();
                        switch (step.ControlType)
                        {
                            case "forEach":
                                await ControlForEach(step, engine, context, result).ConfigureAwait(false);
                                break;
                            case "branch":
                                await ControlBranch(step, engine, context, result).ConfigureAwait(false);
                                break;
                            case "while":
                                await ControlWhile(step, engine, context, stepResultDic).ConfigureAwait(false);
                                break;
                            case "transaction":
                                await Transaction(step, engine, context).ConfigureAwait(false);
                                break;
                            case "tryCatch":
                                await TryCatch(step, engine, context).ConfigureAwait(false);
                                break;
                        }
                        context.BreakCancellationTokenSource = oldBreakToken;
                        context.ContinueCancellationTokenSource = oldContinueToken;
                    }
                }).Unwrap();


            if (step.Wait)
            {
                await task.ConfigureAwait(false);
            }

            // 执行下一步
            await RunAction(step.NextId, engine, context).ConfigureAwait(false);
        }

        internal static string FlowPipelineKeyPrefix => "$Flow_";

        /// <summary>
        /// 捕获异常
        /// </summary>
        async Task TryCatch(FlowStep step, Engine engine, FunctionContext context)
        {
            if (step.Control.TryCatch == null) return;

            var pipelineKey = $"{FlowPipelineKeyPrefix}{step.Function}_{step.Id}_TryCatch";
            ResiliencePipelineManager.TryAdd(pipelineKey, () =>
            {
                var handle = new ResilienceHandle<object>(pipelineKey);

                if (step.Control.TryCatch.AllowRetry && step.Control.TryCatch.RetryCount > 0 && step.Control.TryCatch.RetryDelaysInSeconds > 0)
                {
                    handle.RetryCount = step.Control.TryCatch.RetryCount.Value;
                    handle.RetryDelaysInSeconds = step.Control.TryCatch.RetryDelaysInSeconds.Value;
                }

                return handle.Build();
            });

            await ResiliencePipelineManager.ExecuteAsync(pipelineKey, async (token) =>
            {
                await RunAction(step.Control.TryCatch.NextId, engine, context).ConfigureAwait(false);
            }).ConfigureAwait(false);
        }

        /// <summary>
        /// 分布式事务
        /// </summary>
        async Task Transaction(FlowStep step, Engine engine, FunctionContext context)
        {
            if (step.Control.Transaction == null) return;

            await DbHelper.UseTransactionScope(async () =>
            {
                context.GlobalTransaction.Value = true;
                await RunAction(step.Control.Transaction.NextId, engine, context).ConfigureAwait(false);
                context.GlobalTransaction.Value = false;
            }).ConfigureAwait(false);
        }

        /// <summary>
        /// 流程控制分支
        /// </summary>
        async Task ControlBranch(FlowStep step, Engine engine, FunctionContext context, object boolResult)
        {
            if (step.Control.Branch == null) return;

            var branchInfo = step.Control.Branch.ToDictionary(t => t.Key, t => false);
            foreach (var item in step.Control.Branch)
            {
                // 懒加载：只在需要执行脚本条件时创建引擎
                var needsEngine = !string.IsNullOrEmpty(item.Value.Condition);
                var currentEngine = engine;
                if (currentEngine == null && needsEngine)
                {
                    currentEngine = FlowUtils.GetEngine(context);
                }

                branchInfo[item.Key] = string.IsNullOrEmpty(item.Value.Condition) ? boolResult.Parse<bool>() : currentEngine.Evaluate(item.Value.Condition).AsBoolean();
                if (branchInfo[item.Key])
                {
                    await RunAction(item.Value.NextId, currentEngine, context).ConfigureAwait(false);
                }
            }
        }

        /// <summary>
        /// 流程控制While循环
        /// </summary>
        async Task ControlWhile(FlowStep step, Engine engine, FunctionContext context, Dictionary<string, object> dic)
        {
            if (step.Control.While == null) return;

            var beginTime = DateTime.Now;
            var previousProvider = context.Current;

            // 懒加载：检查是否需要引擎
            var currentEngine = engine;
            var needsEngine = step.Control.While.LoopType != "count" || step.Control.While.LoopCount?.Type == "script";
            if (currentEngine == null && needsEngine)
            {
                currentEngine = FlowUtils.GetEngine(context);
            }

            var i = 0;
            switch (step.Control.While.LoopType)
            {
                case "count":
                    var count = step.Control.While.LoopCount.GetDataValue<int>(currentEngine, globalData: context.globalData);
                    for (i = 1; i <= count; i++)
                    {
                        var result = await WhileFunc(i).ConfigureAwait(false);
                        if (result == ControlOperationType.Break)
                            break;
                        else if (result == ControlOperationType.Continue)
                            continue;
                    }
                    break;
                case "while":
                    var whileCondition = currentEngine.Evaluate(step.Control.While.Condition).AsBoolean();
                    while (whileCondition)
                    {
                        i++;

                        var result = await WhileFunc(i).ConfigureAwait(false);
                        whileCondition = currentEngine.Evaluate(step.Control.While.Condition).AsBoolean();
                        if (result == ControlOperationType.Break)
                            break;
                        else if (result == ControlOperationType.Continue)
                            continue;
                    }
                    break;
                case "doWhile":
                    var doWhileCondition = true;
                    do
                    {
                        i++;
                        var result = await WhileFunc(i).ConfigureAwait(false);
                        doWhileCondition = currentEngine.Evaluate(step.Control.While.Condition).AsBoolean();
                        if (result == ControlOperationType.Break)
                            break;
                        else if (result == ControlOperationType.Continue)
                            continue;
                    } while (doWhileCondition);
                    break;
                case "infinite":
                    while (true)
                    {
                        i++;
                        var result = await WhileFunc(i).ConfigureAwait(false);
                        if (result == ControlOperationType.Break)
                            break;
                        else if (result == ControlOperationType.Continue)
                            continue;
                    }
                    break;
            }

            return;

            async Task<ControlOperationType> WhileFunc(int i)
            {
                context.Current = CreateProvider(context, step, previousProvider);
                
                var stepLog = new LcFruStep
                {
                    Id = TUID.NewTUID().ToString(),
                    ProcId = context.trackId,
                    FunctionId = step.Function,
                    ActionId = context.Current.ActionId,
                    StepName = step.Name + " " + i,
                    SeqNo = context.Current.SeqNo,
                    Status = 1,
                    BeginTime = beginTime,
                    EndTime = DateTime.Now,
                    Duration = 0,
                };
                context.Current.StepId = stepLog.Id;
                context.Current.StepName = step.Name;

                _ = context.SqlLog.Write(stepLog);

                FlowUtils.BindResult(step.Result, dic, engine, context, i);
                if (context.Persistence) _ = context.SqlLog.AddFunctionOutputVariable(dic);

                try
                {
                    await RunAction(step.Control.While.NextId, engine, context).ConfigureAwait(false);
                }
                catch (OperationCanceledException ex)
                {
                    if (ex.CancellationToken == context.BreakCancellationTokenSource?.Token)
                    {
                        return ControlOperationType.Break;
                    }
                    else if (ex.CancellationToken == context.ContinueCancellationTokenSource?.Token)
                    {
                        return ControlOperationType.Continue;
                    }
                }
                finally
                {
                    beginTime = DateTime.Now;
                }
                return ControlOperationType.None;
            }
        }

        /// <summary>
        /// 流程控制ForEach循环
        /// </summary>
        async Task ControlForEach(FlowStep step, Engine engine, FunctionContext context, object listResult)
        {
            if (step.Control.ForEach == null) return;

            // 懒加载：检查是否需要引擎
            var currentEngine = engine;
            var needsEngine = !string.IsNullOrEmpty(step.Control.ForEach.List) || FlowUtils.LazyEngineWrapper.RequiresEngine(step.Control.ForEach.Item);
            if (currentEngine == null && needsEngine)
            {
                currentEngine = FlowUtils.GetEngine(context);
            }

            object listObj = null;
            var listExp = step.Control.ForEach.List;
            listObj = string.IsNullOrEmpty(listExp) ? listResult : currentEngine.Evaluate(listExp).AsArray();

            var dic = context.globalData[step.Id] as Dictionary<string, object>;

            var itemData = step.Control.ForEach.Item;
            var resultKey = FlowUtils.GetResultKey(itemData);
            var hasResult = !string.IsNullOrEmpty(resultKey);

            //// 临时变量
            //var itemKey = "item";
            //dic[itemKey] = new AsyncLocal<object>();

            if (!await ForEachStepLog(step, context, listObj, EachFunc).ConfigureAwait(false))
            {
                await AsyncForEachStepLog(step, context, listObj, EachFunc).ConfigureAwait(false);
            }

            return;

            async Task EachFunc(object t)
            {
                if (hasResult)
                {
                    FlowUtils.BindResult(itemData, dic, currentEngine, context, t);
                    if (context.Persistence) _ = context.SqlLog.AddFunctionOutputVariable(dic);
                }

                await RunAction(step.Control.ForEach.NextId, currentEngine, context).ConfigureAwait(false);
            }
        }

        async Task<bool> ForEachStepLog(FlowStep step, FunctionContext context, object listObj, Func<object, Task> func)
        {
            if (listObj is not ICollection list) return false;

            //var list = listObj as IEnumerable<object>;

            //context.SqlLog.AddFunctionOutputVariable(dic);
            if (context.Persistence)
            {
                var beginTime = DateTime.Now;
                var count = 0;

                var previousProvider = context.Current;

                if (step.Control.ForEach.Async && list is IEnumerable<object> arrayPersistence)
                {
                    Parallel.ForEach(arrayPersistence, t => _ = ItemFunc(t).ConfigureAwait(false));
                }
                else
                {

                    foreach (var item in list)
                    {
                        try
                        {
                            await ItemFunc(item).ConfigureAwait(false);
                        }
                        catch (OperationCanceledException ex)
                        {
                            if (ex.CancellationToken == context.BreakCancellationTokenSource?.Token)
                            {
                                break;
                            }
                            else if (ex.CancellationToken == context.ContinueCancellationTokenSource?.Token)
                            {
                                continue;
                            }
                        }
                    }
                }
                return true;

                async Task ItemFunc(object item)
                {
                    count++;

                    context.Current = CreateProvider(context, step, previousProvider);

                    var stepLog = new LcFruStep
                    {
                        Id = TUID.NewTUID().ToString(),
                        ProcId = context.trackId,
                        FunctionId = step.Function,
                        ActionId = context.Current.ActionId,
                        StepName = step.Name + " " + count,
                        SeqNo = context.Current.SeqNo,
                        Status = 1,
                        BeginTime = beginTime,
                        EndTime = DateTime.Now,
                        Duration = 0,
                    };
                    context.Current.StepId = stepLog.Id;
                    context.Current.StepName = step.Name;

                    _ = context.SqlLog.Write(stepLog);

                    await func(item).ConfigureAwait(false);
                    beginTime = DateTime.Now;
                }
            }


            if (step.Control.ForEach.Async && list is IEnumerable<object> array)
            {
                Parallel.ForEach(array, t => _ = func(t).ConfigureAwait(false));
            }
            else
            {
                foreach (var item in list)
                {
                    try
                    {
                        await func(item).ConfigureAwait(false);
                    }
                    catch (OperationCanceledException ex)
                    {
                        if (ex.CancellationToken == context.BreakCancellationTokenSource?.Token)
                        {
                            break;
                        }
                        else if (ex.CancellationToken == context.ContinueCancellationTokenSource?.Token)
                        {
                            continue;
                        }
                    }
                }
            }
            return true;
        }

        async Task<bool> AsyncForEachStepLog(FlowStep step, FunctionContext context, object listObj, Func<object, Task> func)
        {
            var asyncList = listObj as IAsyncEnumerable<List<object>>;
            if (asyncList == null) return false;

            if (context.Persistence)
            {
                var sw = new Stopwatch();
                var beginTime = DateTime.Now;
                var isFirst = true;
                var count = 0;

                var previousProvider = context.Current;
                sw.Start();
                await foreach (var item in asyncList.ConfigureAwait(false))
                {
                    if (isFirst)
                    {
                        sw.Stop();
                        isFirst = false;
                    }

                    count++;

                    context.Current = CreateProvider(context, step, previousProvider);
                    var stepLog = new LcFruStep
                    {
                        Id = TUID.NewTUID().ToString(),
                        ProcId = context.trackId,
                        FunctionId = step.Function,
                        ActionId = context.Current.ActionId,
                        StepName = step.Name + " " + count,
                        SeqNo = context.Current.SeqNo,
                        Status = 1,
                        BeginTime = beginTime,
                        EndTime = DateTime.Now,
                        Duration = (int)sw.ElapsedMilliseconds,
                    };
                    context.Current.StepId = stepLog.Id;
                    context.Current.StepName = step.Name;

                    _ = context.SqlLog.Write(stepLog);

                    _ = context.SqlLog.Info($"查询成功 {step.Name}, 查询数据量 {item.Count}", true);

                    try
                    {
                        await func(item).ConfigureAwait(false);
                    }
                    catch (OperationCanceledException ex)
                    {
                        if (ex.CancellationToken == context.BreakCancellationTokenSource?.Token)
                        {
                            break;
                        }
                        else if (ex.CancellationToken == context.ContinueCancellationTokenSource?.Token)
                        {
                            continue;
                        }
                    }
                    sw.Restart();
                    beginTime = DateTime.Now;
                }
                sw.Stop();
                return true;
            }

            await foreach (var item in asyncList.ConfigureAwait(false))
            {
                try
                {
                    await func(item).ConfigureAwait(false);
                }
                catch (OperationCanceledException ex)
                {
                    if (ex.CancellationToken == context.BreakCancellationTokenSource?.Token)
                    {
                        break;
                    }
                    else if (ex.CancellationToken == context.ContinueCancellationTokenSource?.Token)
                    {
                        continue;
                    }
                }
            }
            return true;
        }
    }
}
