using GCP.Common;
using GCP.DataAccess;
using GCP.FunctionPool.Flow.Models;
using GCP.Functions.Common;
using GCP.Functions.Common.ScriptExtensions;
using GCP.Functions.Common.VisualFunction;
using Jint;
using Serilog;
using System.Collections;
using System.Text.Json;

namespace GCP.FunctionPool.Flow
{
    internal static class FlowUtils
    {
        private static object GetVariableValue(object current, string[] parts, int pathIndex)
        {
            if (pathIndex >= parts.Length)
            {
                return current;
            }

            string part = parts[pathIndex];
            if (part.Contains('['))
            {
                // 处理数组索引
                int indexStart = part.IndexOf('[');
                int indexEnd = part.IndexOf(']');
                string key = part[..indexStart];
                string indexStr = part.Substring(indexStart + 1, indexEnd - indexStart - 1);

                if (current is IDictionary<string, object> dict && dict.TryGetValue(key, out var value))
                {
                    current = value;
                    if (current is ICollection list)
                    {
                        var arr = new ArrayList(list);
                        if (string.IsNullOrEmpty(indexStr) || !int.TryParse(indexStr, out var arrayIndex))
                        {
                            return arr;
                        }

                        if (arrayIndex >= 0 && arrayIndex < list.Count)
                        {
                            current = arr[arrayIndex];
                        }
                    }
                    else
                    {
                        Log.Error("变量 {Join} : {Part} 不支持从当前数组中取值，{Serialize}", parts.Join("."), part, JsonHelper.Serialize(current));
                        return null; // 数组不存在或索引超出范围
                    }
                }
                else
                {
                    Log.Error("未定义的变量：{Part} 在字典中【{Serialize}】不存在", part, JsonHelper.Serialize(current));
                    return null; // 键不存在
                }
            }
            else
            {
                if (current is IDictionary<string, object> dict)
                {
                    if (dict.TryGetValue(part, out object value))
                    {
                        current = value;
                    }
                    else
                    {
                        // 特殊处理ROOT节点：如果访问ROOT但字典中没有ROOT字段，
                        // 且字典中只有一个result字段，则将result的值作为ROOT节点的值
                        if (part == RootNodeHelper.RootNodeKey &&
                            dict.Count == 1)
                        {
                            current = dict.First().Value;
                        }
                        else
                        {
                            throw new CustomException($"未定义的变量：{part} 在字段中【{dict.Keys.Join(", ")}】不存在");
                        }
                    }
                }
                else if (current is ICollection list)
                {
                    var result = new List<object>();
                    var arr = new ArrayList(list);
                    foreach (var item in arr)
                    {
                        if (item is not IDictionary<string, object> itemDict) continue;
                        if (itemDict.TryGetValue(part, out var value))
                        {
                            result.Add(value);
                        }
                        else
                        {
                            throw new CustomException($"未定义的变量：{part} 在数组中【{itemDict.Keys.Join(", ")}】不存在");
                        }
                    }
                    return result.Distinct().ToList();
                }
                else
                {
                    Log.Error("变量 {Join} : {Part} 不支持从当前数据中取值，{Serialize}", parts.Join("."), part, JsonHelper.Serialize(current));
                    return null; // 键不存在
                }
            }

            return GetVariableValue(current, parts, pathIndex + 1);
        }

        internal static object GetVariableValueByPath(this DataValue value, Dictionary<string, object> globalData, Dictionary<string, object> localVariable = null)
        {
            var parts = value.VariableValue.Split('.', StringSplitOptions.RemoveEmptyEntries);
            var varDic = value.VariableType == "current" ? localVariable : globalData;

            // 检查是否需要处理ROOT节点
            // 如果变量路径指向的数据包含ROOT节点，需要特殊处理
            var result = GetVariableValue(varDic, parts, 0);

            // 使用ROOT节点兼容性处理
            result = RootNodeHelper.ProcessVariablePathResult(result, value.VariableValue);

            if (result is AsyncLocal<object> asyncLocal)
            {
                result = asyncLocal.Value;
            }

            //if (value is IDictionary<string, object> dict)
            //{
            //    result = new Dictionary<string, object>(dict);
            //}

            //if (result is ICollection arr)
            //{
            //    var objArr = new object[arr.Count];
            //    arr.CopyTo(objArr, 0);
            //    result = new ArrayList(objArr);
            //}

            return result;
        }

        internal static object GetDataValue(this DataValue value, FunctionContext context, Engine engine = null, Dictionary<string, object> localVariable = null, Func<string, object> scriptGetValue = null)
        {
            return GetDataValue(value, engine, context.globalData, localVariable, scriptGetValue);
        }

        internal static T GetDataValue<T>(this DataValue value, Engine engine = null, Dictionary<string, object> globalData = null, Dictionary<string, object> localVariable = null, Func<string, object> scriptGetValue = null, T defaultValue = default)
        {
            object result = GetDataValue(value, engine, globalData, localVariable, scriptGetValue);
            if (result == null) return defaultValue;
            if (result is JsonElement) return JsonHelper.Deserialize<T>(result.ToString());
            return result.Parse(defaultValue);
        }

        internal static object GetDataValue(this DataValue value, string type, FunctionContext context = null, Engine engine = null, Dictionary<string, object> localVariable = null, Func<string, object> scriptGetValue = null)
        {
            object result = GetDataValue(value, engine, context?.globalData, localVariable, scriptGetValue);
            return value.ParseValue(type, result);
        }

        internal static object ParseValue(this DataValue value, string type, object result)
        {
            if (result == null || (value?.Type == "text" && string.IsNullOrEmpty(result.ToString())))
            {
                if (type == "array")
                {
                    return new ArrayList();
                }
                else if (type == "object")
                {
                    return new Dictionary<string, object>();
                }
            }
            else if (value?.Type == "variable")
            {
                if (result is string str)
                {
                    if (type == "object" || type == "array")
                    {
                        try
                        {
                            // 尝试解析JSON，转换为对象
                            return new JavascriptUtils().JSON_PARSE(str);
                        }
                        catch (Exception ex)
                        {
                            throw new CustomException("文本转换字典失败，" + "[" + str + "] " + ex.Message);
                        }
                    }
                }
                else if (result is ICollection arr)
                {
                    var isDict = result is IDictionary<string, object>;
                    if (type != "array" && !(type == "object" && isDict))
                    {
                        var enumerator = arr.GetEnumerator();
                        if (enumerator.MoveNext())
                        {
                            result = enumerator.Current;
                        }
                        else
                        {
                            result = null;
                        }
                    }
                    else if (type == "array" && isDict)
                    {
                        return new ArrayList
                        {
                            result
                        };
                    }
                }
            }
            return result.Parse(type);
        }

        internal static object ParseDefaultValue(this FlowData flowData, object result, FunctionContext context = null, Engine engine = null, Dictionary<string, object> localVariable = null, Func<string, object> scriptGetValue = null)
        {
            var notHasResult = result == null;
            if (flowData.Type == "string")
            {
                notHasResult = string.IsNullOrEmpty(result?.ToString());
            }

            if (flowData.Required.HasValue && flowData.Required.Value && notHasResult)
            {
                throw new CustomException($"数据[{flowData.Key}]不能为空");
            }

            // 如果值为空，且有默认值，则使用默认值
            if (notHasResult && flowData.Value != null)
            {
                return flowData.Value.GetDataValue(flowData.Type, context, engine, localVariable, scriptGetValue);
            }

            return flowData.Value.ParseValue(flowData.Type, result);
        }

        internal static object GetDataValue(DataValue value, Engine engine = null, Dictionary<string, object> globalData = null, Dictionary<string, object> localVariable = null, Func<string, object> scriptGetValue = null)
        {
            if (value == null) return null;

            if (value.Type == "text")
            {
                return value.TextValue;
            }
            else if (value.Type == "variable")
            {
                return GetVariableValueByPath(value, globalData, localVariable);
            }
            else if (value.Type == "script")
            {
                if (engine == null)
                {
                    engine = GetEngine();
                    engine.SetEngineValue(localVariable);
                }
                if (scriptGetValue == null)
                {
                    var jsValue = engine.Evaluate(value.ScriptValue);
                    return jsValue.GetObject();
                }
                else
                {
                    //if (!prepareScripts.TryGetValue(value.ScriptValue, out var preparedScript))
                    //{
                    //    prepareScripts.Add(value.ScriptValue, Engine.PrepareScript(value.ScriptValue));
                    //}

                    //var jsValue = engine.Evaluate(preparedScript);
                    //return jsValue.ToObject();
                    return scriptGetValue(value.ScriptValue);
                }
            }
            else if (value.Type == "visual")
            {
                return ExecuteVisualFunction(value, globalData, localVariable);
            }
            return null;
        }

        internal static void SetEngineValue(this Engine engine, Dictionary<string, object> localVariable)
        {
            if (localVariable != null && localVariable.Count > 0)
            {
                foreach (var item in localVariable)
                {
                    engine.SetValue(item.Key, item.Value);
                }
            }
        }

        /// <summary>
        /// 执行可视化函数
        /// </summary>
        /// <param name="value">包含可视化函数配置的DataValue</param>
        /// <param name="db">数据库上下文</param>
        /// <param name="globalData">全局数据</param>
        /// <param name="localVariable">局部变量</param>
        /// <returns>执行结果</returns>
        internal static object ExecuteVisualFunction(DataValue value, Dictionary<string, object> globalData, Dictionary<string, object> localVariable)
        {
            try
            {
                if (value.VisualSteps == null)
                {
                    return null;
                }

                // 反序列化可视化函数步骤
                var steps = value.VisualSteps;
                if (steps == null || steps.Count == 0)
                {
                    return null;
                }

                // 准备输入数据
                var inputData = new Dictionary<string, object>();
                if (globalData != null)
                {
                    foreach (var item in globalData)
                    {
                        inputData[item.Key] = item.Value;
                    }
                }
                if (localVariable != null)
                {
                    foreach (var item in localVariable)
                    {
                        inputData[item.Key] = item.Value;
                    }
                }

                // 创建可视化函数引擎并执行
                var engine = new VisualFunctionEngine();
                var result = engine.ExecuteAsync(steps, inputData).GetAwaiter().GetResult();

                return result;
            }
            catch (Exception ex)
            {
                throw new CustomException($"执行可视化函数失败: {ex.Message}", ex);
            }
        }

        //private static Engine engine;
        //private static CancellationConstraint constraint;
        internal static Engine GetEngine()
        {
            //if (engine == null)
            //{
            //    engine = new Engine(options =>
            //    {
            //        options.CancellationToken(new CancellationToken(true));
            //    });
            //    constraint = engine.Constraints.Find<CancellationConstraint>();

            //    engine.SetValue("Utils", new JavascriptUtils(db));
            //}

            //using var tcs = new CancellationTokenSource(TimeSpan.FromSeconds(10));
            //constraint.Reset(tcs.Token);

            var engine = JavascriptEngine.CreateEngine();
            engine.SetValue("Utils", new JavascriptUtils());
            return engine;
        }

        internal static Engine GetEngine(FunctionContext context)
        {
            var engine = GetEngine();
            engine.JavascriptFuncBeforeExec(context);

            // 使用兼容的_data变量，确保脚本中的_data.xxx.xxx访问方式仍然有效
            //var compatibleData = RootNodeHelper.PrepareScriptCompatibleData(context.globalData);
            //engine.SetValue("_data", compatibleData);
            engine.SetValue("_data", context.globalData);

            return engine;
        }


        internal static string GetResultKey(List<FlowData> flowDataList)
        {
            var resultData = flowDataList.FirstOrDefault(t => t.Value is
                { Type: "variable", VariableType: "current", VariableValue: CurrentResultKey });
            return resultData != null ? resultData.Key : "";
        }

        private const string CurrentResultKey = "result";
        internal static object BindResult(List<FlowData> flowDataList, Dictionary<string, object> dic, Engine engine, FunctionContext context, object result = null, Dictionary<string, object> localVariable = null)
        {
            var hasSteps = flowDataList is { Count: > 0 };

            if (hasSteps)
            {
                localVariable ??= [];
                localVariable[CurrentResultKey] = result;
                engine.SetValue(CurrentResultKey, result);
                
                BindData(flowDataList, dic, engine, context, localVariable);
                return RootNodeHelper.ProcessResult(flowDataList, dic, result) ?? dic;
            }
            return null;
        }

        private static void BindData(List<FlowData> flowDataList, Dictionary<string, object> dic, Engine engine, FunctionContext context, Dictionary<string, object> localVariable = null, IDictionary<string, object> data = null, Dictionary<string, string> parentKeys = null)
        {
            localVariable ??= [];
            parentKeys ??= [];
            var hasData = data != null;
            var hasParentKey = parentKeys is { Count: > 0 };

            void SetValue(string key, object obj, bool isCover = true)
            {
                if (hasData)
                {
                    if (isCover)
                    {
                        data[key] = obj;
                    }
                    else
                    {
                        if (!data.ContainsKey(key))
                        {
                            data.Add(key, obj);
                        }
                    }
                }
                else
                {
                    dic[key] = obj;
                    //engine.SetValue(t.Key, value);
                }
            }

            foreach (var t in flowDataList)
            {
                if (string.IsNullOrEmpty(t.Key)) continue;
                var hasChildren = t.Children is { Count: > 0 };
                var isCustomize = (t.IsCustomize.HasValue && t.IsCustomize.Value) || (hasChildren && t.Children.Any(t => t.IsCustomize.HasValue && t.IsCustomize.Value));
                object value = null;

                if (hasData)
                {
                    if (data.ContainsKey(t.Key) && !isCustomize)
                    {
                        continue;
                    }
                }

                if (t.Value == null || (t.Value.Type == "text" && string.IsNullOrEmpty(t.Value.TextValue)))
                {
                    if (t.Type is "array" or "object")
                    {
                        t.Value = new DataValue()
                        {
                            Type = "text"
                        };
                    }
                    else
                    {
                        //导致只设置字段，而不设置值场景（这种场景一般是保留字段给后续步骤使用），导致取值为空，新增isCover字段，默认覆盖
                        SetValue(t.Key, null, false);
                        continue;
                    }
                }

                var isVariable = t.Value.Type == "variable";
                var tempVar = t.Value.VariableValue;
                if (hasParentKey && isVariable)
                {
                    for (int i = parentKeys.Count - 1; i >= 0; i--)
                    {
                        var item = parentKeys.ElementAt(i);
                        if (tempVar.StartsWith(item.Key + "."))
                        {
                            t.Value.VariableValue = item.Value + tempVar.TrimStart(item.Key);
                            t.Value.VariableType = "current";
                            break;
                        }
                    }
                }

                if (context.Current != null)
                {
                    context.Current.ScriptName = t.Value.ScriptName;
                }
                value = t.Value.GetDataValue(t.Type, context, engine, localVariable);

                t.Value.VariableValue = tempVar;

                // 数据类型转换处理（移除ROOT节点处理，避免中间过程的性能损失）
                if (t.Type == "array")
                {
                    var list = new ArrayList();

                    if (value is JsonElement { ValueKind: JsonValueKind.Array } arrJson)
                    {
                        foreach (var item in arrJson.EnumerateArray())
                        {
                            list.Add(new JavascriptUtils().JSON_PARSE(item.GetRawText()));
                        }
                        value = list;
                    }
                }
                else if (t.Type == "object")
                {
                    if (value is JsonElement { ValueKind: JsonValueKind.Object } json)
                    {
                        value = new JavascriptUtils().JSON_PARSE(json.GetRawText());
                    }
                }

                // 验证是否为空
                if (t.Required.HasValue && t.Required.Value)
                {
                    var notHasValue = t.Value == null;
                    if (t.Type == "string")
                    {
                        notHasValue = string.IsNullOrEmpty(value.ToString());
                    }

                    if (notHasValue)
                    {
                        throw new CustomException($"字段[{tempVar ?? t.Key}]不能为空");
                    }
                }

                SetValue(t.Key, value);

                if (!isCustomize) continue;
                if (!hasChildren) continue;

                if (t.Type == "array")
                {
                    var list = new ArrayList();

                    if (value is ICollection arr)
                    {
                        list = new ArrayList(arr);
                    }
                    else
                    {
                        list.Add(value);
                        dic[t.Key] = list;
                    }

                    var itemKey = "item";
                    if (isVariable)
                    {
                        itemKey = t.Value.VariableValue.replace(".", "_");
                        parentKeys[t.Value.VariableValue] = itemKey;
                    }

                    var oldItemValue = engine.GetValue(itemKey);
                    for (int i = 0; i < list.Count; i++)
                    {
                        var item = list[i];
                        engine.SetValue(itemKey, item);
                        localVariable[itemKey] = item;
                        if (item is IDictionary<string, object> dict)
                        {
                            dict = new Dictionary<string, object>(dict);
                            BindData(t.Children, dic, engine, context, localVariable, dict, parentKeys);
                            list[i] = dict;
                        }
                        else if (item is List<object> { Count: > 0 } dicList && dicList[0] is KeyValuePair<string, object>)
                        {
                            var listDict = new Dictionary<string, object>();
                            foreach (var itemDict in dicList)
                            {
                                if (itemDict is KeyValuePair<string, object> keyValue)
                                    listDict.Add(keyValue.Key, keyValue.Value);
                            }
                            BindData(t.Children, dic, engine, context, localVariable, listDict, parentKeys);
                            list[i] = listDict;
                        }
                        else
                        {
                            _ = context.SqlLog.Warn("循环数组元素必须为字典，当前为：" + item?.GetType().Name);
                        }
                    }
                    engine.SetValue(itemKey, oldItemValue);
                    SetValue(t.Key, list);
                }
                else if (t.Type == "object")
                {
                    if (value is IDictionary<string, object> dict)
                    {
                        dict = new Dictionary<string, object>(dict);
                        BindData(t.Children, dic, engine, context, localVariable, dict);
                        SetValue(t.Key, dict);
                    }
                    else
                    {
                        _ = context.SqlLog.Warn("字典元素格式错误，当前为：" + value?.GetType().Name);
                    }
                }
            }

            if (hasData)
            {
                foreach (var dicItem in data)
                {
                    if (flowDataList.All(t => t.Key != dicItem.Key))
                    {
                        data.Remove(dicItem.Key);
                    }
                }
            }
        }

        private static readonly string andMultiPrefix = "AND (";
        private static readonly string orMultiPrefix = "OR (";

        internal static bool SetCondition<T>(this SqlBuilder<T> sb, ConditionInfo condition, string tableAlias = null, bool isAnd = true, Engine engine = null, Dictionary<string, object> globalData = null, string multiPrefix = null, int level = 0)
        {
            if (condition == null) return false;

            if (condition.Type == ConditionInfoType.Node)
            {
                var hasCondition = false;
                if (condition.Operator == ConditionOperatorType.AND)
                {
                    for (int j = 0; j < condition.Children.Count; j++)
                    {
                        var result = SetCondition(sb, condition.Children[j], tableAlias, true, engine, globalData, hasCondition ? null : andMultiPrefix, level + 1);
                        if (result) hasCondition = true;
                    }
                }
                else if (condition.Operator == ConditionOperatorType.OR)
                {
                    for (int j = 0; j < condition.Children.Count; j++)
                    {
                        var result = SetCondition(sb, condition.Children[j], tableAlias, false, engine, globalData, hasCondition ? null : (level == 0 ? andMultiPrefix : orMultiPrefix), level + 1);
                        if (result) hasCondition = true;
                    }
                }

                if (hasCondition)
                {
                    sb.Append(")");
                }
            }
            else if (condition.Type == ConditionInfoType.Leaf)
            {
                var andOr = isAnd ? "AND " : "OR ";
                var prefix = string.IsNullOrEmpty(multiPrefix) ? andOr : multiPrefix;
                var alias = string.IsNullOrEmpty(tableAlias) ? "" : tableAlias + ".";
                var result = GetDataValue(condition.Value, engine, globalData: globalData);
                var isNull = DbHelper.IsNull(result);
                if (condition.IsFilter && isNull) return false;

                //if (!string.IsNullOrEmpty(condition.Value.DataType))
                //{
                //    result = condition.Value.ParseValue(condition.Value.DataType, result);
                //}
                sb.AppendCondition(prefix + " " + alias + condition.Column, condition.Operator, result);
            }
            return true;
        }

        internal static bool GetConditionValue(ConditionInfo condition, int i = -1, Engine engine = null, FunctionContext context = null)
        {
            if (condition == null) return false;

            if (condition.Type == ConditionInfoType.Node)
            {
                if (condition.Operator == ConditionOperatorType.AND)
                {
                    for (int j = 0; j < condition.Children.Count; j++)
                    {
                        if (!GetConditionValue(condition.Children[j], j, engine, context)) return false;
                    }
                    return true;
                }
                else if (condition.Operator == ConditionOperatorType.OR)
                {
                    for (int j = 0; j < condition.Children.Count; j++)
                    {
                        if (GetConditionValue(condition.Children[j], j, engine, context)) return true;
                    }
                    return false;
                }
            }
            else if (condition.Type == ConditionInfoType.Leaf)
            {
                if (condition.ColumnValue == null || condition.Value == null)
                {
                    throw new CustomException("条件不能为空");
                }
                var leftValue = condition.ColumnValue.GetDataValue(condition.ColumnValue.DataType, context, engine);
                var rightValue = condition.Value.GetDataValue(condition.ColumnValue.DataType, context, engine);
                return condition.Operator switch
                {
                    ConditionOperatorType.Equals => leftValue.Equals(rightValue),
                    ConditionOperatorType.NotEquals => !leftValue.Equals(rightValue),
                    ConditionOperatorType.GreaterThan => leftValue.Parse<double>() > rightValue.Parse<double>(),
                    ConditionOperatorType.GreaterThanOrEqual => leftValue.Parse<double>() >= rightValue.Parse<double>(),
                    ConditionOperatorType.LessThan => leftValue.Parse<double>() < rightValue.Parse<double>(),
                    ConditionOperatorType.LessThanOrEqual => leftValue.Parse<double>() <= rightValue.Parse<double>(),
                    ConditionOperatorType.Like => leftValue.Parse<string>().Contains(rightValue.Parse<string>()),
                    ConditionOperatorType.LeftLike => leftValue.Parse<string>().StartsWith(rightValue.Parse<string>()),
                    ConditionOperatorType.RightLike => leftValue.Parse<string>().EndsWith(rightValue.Parse<string>()),
                    ConditionOperatorType.NotLike => !leftValue.Parse<string>().Contains(rightValue.Parse<string>()),
                    ConditionOperatorType.In => rightValue.Parse<List<object>>().Contains(leftValue),
                    ConditionOperatorType.NotIn => !rightValue.Parse<List<object>>().Contains(leftValue),
                    _ => false,
                };
            }
            return false;
        }
    }
}
