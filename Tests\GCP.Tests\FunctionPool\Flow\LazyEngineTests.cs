using GCP.Common;
using GCP.DataAccess;
using GCP.FunctionPool.Flow;
using GCP.Tests.Infrastructure;
using System.Diagnostics;
using Xunit;
using Xunit.Abstractions;

namespace GCP.Tests.FunctionPool.Flow
{
    public class LazyEngineTests : DatabaseTestBase
    {
        private readonly ITestOutputHelper _output;

        public LazyEngineTests(ITestOutputHelper output) : base(output)
        {
            _output = output;
        }

        [Fact]
        public void LazyEngineWrapper_RequiresEngine_ShouldDetectScriptType()
        {
            // Arrange
            var flowDataWithScript = new List<FlowData>
            {
                new FlowData
                {
                    Key = "test",
                    Type = "string",
                    Value = new DataValue { Type = "script", ScriptValue = "return 'hello';" }
                }
            };

            var flowDataWithoutScript = new List<FlowData>
            {
                new FlowData
                {
                    Key = "test",
                    Type = "string",
                    Value = new DataValue { Type = "text", TextValue = "hello" }
                }
            };

            // Act & Assert
            Assert.True(FlowUtils.LazyEngineWrapper.RequiresEngine(flowDataWithScript));
            Assert.False(FlowUtils.LazyEngineWrapper.RequiresEngine(flowDataWithoutScript));
        }

        [Fact]
        public void LazyEngineWrapper_RequiresEngine_ShouldDetectNestedScript()
        {
            // Arrange
            var flowDataWithNestedScript = new List<FlowData>
            {
                new FlowData
                {
                    Key = "parent",
                    Type = "object",
                    Value = new DataValue { Type = "text", TextValue = "parent" },
                    Children = new List<FlowData>
                    {
                        new FlowData
                        {
                            Key = "child",
                            Type = "string",
                            Value = new DataValue { Type = "script", ScriptValue = "return 'child';" }
                        }
                    }
                }
            };

            // Act & Assert
            Assert.True(FlowUtils.LazyEngineWrapper.RequiresEngine(flowDataWithNestedScript));
        }

        [Fact]
        public void LazyEngineWrapper_ShouldCreateEngineOnlyWhenNeeded()
        {
            // Arrange
            var localVariable = new Dictionary<string, object> { { "test", "value" } };
            var lazyEngine = new FlowUtils.LazyEngineWrapper(null, Context, localVariable);

            // Act - 调用GetEngine应该创建引擎
            var engine = lazyEngine.GetEngine();

            // Assert
            Assert.NotNull(engine);
            
            // 验证引擎已经设置了本地变量
            var testValue = engine.GetValue("test");
            Assert.Equal("value", testValue.ToString());
        }

        [Fact]
        public void BindResult_WithoutScriptData_ShouldNotCreateEngine()
        {
            // Arrange
            var flowData = new List<FlowData>
            {
                new FlowData
                {
                    Key = "simple_text",
                    Type = "string",
                    Value = new DataValue { Type = "text", TextValue = "Hello World" }
                }
            };

            var globalData = new Dictionary<string, object>();
            var localVariable = new Dictionary<string, object>();

            // Act
            var stopwatch = Stopwatch.StartNew();
            var result = FlowUtils.BindResult(flowData, globalData, null, Context, null, localVariable);
            stopwatch.Stop();

            // Assert
            Assert.NotNull(result);
            Assert.True(globalData.ContainsKey("simple_text"));
            Assert.Equal("Hello World", globalData["simple_text"]);
            
            _output.WriteLine($"无脚本数据处理耗时: {stopwatch.ElapsedMilliseconds}ms");
            
            // 应该非常快，因为没有创建引擎
            Assert.True(stopwatch.ElapsedMilliseconds < 100, 
                $"无脚本处理应该很快，实际耗时: {stopwatch.ElapsedMilliseconds}ms");
        }

        [Fact]
        public void BindResult_WithScriptData_ShouldCreateEngineOnlyWhenNeeded()
        {
            // Arrange
            var flowData = new List<FlowData>
            {
                new FlowData
                {
                    Key = "simple_text",
                    Type = "string",
                    Value = new DataValue { Type = "text", TextValue = "Hello" }
                },
                new FlowData
                {
                    Key = "script_result",
                    Type = "string",
                    Value = new DataValue { Type = "script", ScriptValue = "return 'World';" }
                }
            };

            var globalData = new Dictionary<string, object>();
            var localVariable = new Dictionary<string, object>();

            // Act
            var stopwatch = Stopwatch.StartNew();
            var result = FlowUtils.BindResult(flowData, globalData, null, Context, null, localVariable);
            stopwatch.Stop();

            // Assert
            Assert.NotNull(result);
            Assert.True(globalData.ContainsKey("simple_text"));
            Assert.True(globalData.ContainsKey("script_result"));
            Assert.Equal("Hello", globalData["simple_text"]);
            Assert.Equal("World", globalData["script_result"]);
            
            _output.WriteLine($"混合数据处理耗时: {stopwatch.ElapsedMilliseconds}ms");
        }

        [Fact]
        public void PerformanceComparison_LazyVsEager()
        {
            // Arrange - 创建大量无脚本数据
            var flowData = new List<FlowData>();
            for (int i = 0; i < 1000; i++)
            {
                flowData.Add(new FlowData
                {
                    Key = $"item_{i}",
                    Type = "string",
                    Value = new DataValue { Type = "text", TextValue = $"Value {i}" }
                });
            }

            var globalData1 = new Dictionary<string, object>();
            var globalData2 = new Dictionary<string, object>();
            var localVariable = new Dictionary<string, object>();

            // Act - 测试懒加载版本（传入null引擎）
            var lazyStopwatch = Stopwatch.StartNew();
            FlowUtils.BindResult(flowData, globalData1, null, FunctionContext, null, localVariable);
            lazyStopwatch.Stop();

            // Act - 测试预创建引擎版本
            var eagerStopwatch = Stopwatch.StartNew();
            var engine = FlowUtils.GetEngine(FunctionContext);
            FlowUtils.BindResult(flowData, globalData2, engine, FunctionContext, null, localVariable);
            eagerStopwatch.Stop();

            // Assert
            Assert.Equal(globalData1.Count, globalData2.Count);
            
            _output.WriteLine($"懒加载版本耗时: {lazyStopwatch.ElapsedMilliseconds}ms");
            _output.WriteLine($"预创建引擎版本耗时: {eagerStopwatch.ElapsedMilliseconds}ms");
            
            // 懒加载版本应该更快，因为没有创建不必要的引擎
            Assert.True(lazyStopwatch.ElapsedMilliseconds <= eagerStopwatch.ElapsedMilliseconds + 20, 
                $"懒加载应该不慢于预创建引擎版本。懒加载: {lazyStopwatch.ElapsedMilliseconds}ms, 预创建: {eagerStopwatch.ElapsedMilliseconds}ms");
        }
    }
}
