using GCP.Common;
using GCP.FunctionPool;
using GCP.FunctionPool.Flow;
using System.Diagnostics;
using Xunit;
using Xunit.Abstractions;

namespace GCP.Tests.FunctionPool.Flow
{
    public class LazyEngineTests
    {
        private readonly ITestOutputHelper _output;

        public LazyEngineTests(ITestOutputHelper output)
        {
            _output = output;
        }

        private FunctionContext CreateTestContext()
        {
            return new FunctionContext
            {
                globalData = new Dictionary<string, object>(),
                Current = new FunctionProvider { StepName = "TestStep" }
            };
        }

        [Fact]
        public void LazyEngineWrapper_RequiresEngine_ShouldDetectScriptType()
        {
            // Arrange
            var flowDataWithScript = new List<FlowData>
            {
                new FlowData
                {
                    Key = "test",
                    Type = "string",
                    Value = new DataValue { Type = "script", ScriptValue = "return 'hello';" }
                }
            };

            var flowDataWithoutScript = new List<FlowData>
            {
                new FlowData
                {
                    Key = "test",
                    Type = "string",
                    Value = new DataValue { Type = "text", TextValue = "hello" }
                }
            };

            // Act & Assert
            Assert.True(FlowUtils.LazyEngineWrapper.RequiresEngine(flowDataWithScript));
            Assert.False(FlowUtils.LazyEngineWrapper.RequiresEngine(flowDataWithoutScript));
        }

        [Fact]
        public void LazyEngineWrapper_RequiresEngine_ShouldDetectNestedScript()
        {
            // Arrange
            var flowDataWithNestedScript = new List<FlowData>
            {
                new FlowData
                {
                    Key = "parent",
                    Type = "object",
                    Value = new DataValue { Type = "text", TextValue = "parent" },
                    Children = new List<FlowData>
                    {
                        new FlowData
                        {
                            Key = "child",
                            Type = "string",
                            Value = new DataValue { Type = "script", ScriptValue = "return 'child';" }
                        }
                    }
                }
            };

            // Act & Assert
            Assert.True(FlowUtils.LazyEngineWrapper.RequiresEngine(flowDataWithNestedScript));
        }

        [Fact]
        public void LazyEngineWrapper_ShouldCreateEngineOnlyWhenNeeded()
        {
            // Arrange
            var localVariable = new Dictionary<string, object> { { "test", "value" } };

            // 创建一个简单的引擎来测试懒加载包装器
            var testEngine = FlowUtils.GetEngine();
            var lazyEngine = new FlowUtils.LazyEngineWrapper(testEngine, null, localVariable);

            // Act - 调用GetEngine应该返回引擎
            var engine = lazyEngine.GetEngine();

            // Assert
            Assert.NotNull(engine);

            // 验证引擎已经设置了本地变量
            var testValue = engine.GetValue("test");
            Assert.Equal("value", testValue.ToString());
        }

        [Fact]
        public void FlowExecutor_ShouldPreAnalyzeEngineRequirements()
        {
            // Arrange - 创建一个包含脚本和非脚本步骤的流程
            var flow = new FunctionFlow
            {
                Id = "test-flow",
                Version = 1,
                Data = new List<FlowData>
                {
                    new FlowData
                    {
                        Key = "init_data",
                        Type = "string",
                        Value = new DataValue { Type = "text", TextValue = "初始化数据" }
                    }
                },
                Body = new List<FlowStep>
                {
                    new FlowStep
                    {
                        Id = "step1",
                        Function = "test-function",
                        Result = new List<FlowData>
                        {
                            new FlowData
                            {
                                Key = "simple_result",
                                Type = "string",
                                Value = new DataValue { Type = "text", TextValue = "简单结果" }
                            }
                        }
                    },
                    new FlowStep
                    {
                        Id = "step2",
                        Function = "test-function-with-script",
                        Result = new List<FlowData>
                        {
                            new FlowData
                            {
                                Key = "script_result",
                                Type = "string",
                                Value = new DataValue { Type = "script", ScriptValue = "return 'script result';" }
                            }
                        }
                    }
                }
            };

            // Act - 创建FlowExecutor并加载流程
            var executor = new GCP.FunctionPool.FlowExecutor(flow);

            // Assert - 验证预分析结果
            // 由于FlowExecutor的字段是私有的，我们通过行为来验证
            // 这里主要验证加载过程没有异常，实际的预分析效果会在运行时体现
            Assert.NotNull(executor);

            _output.WriteLine("流程预分析完成，引擎需求已缓存");
        }

        [Fact]
        public void PreAnalysis_ShouldImprovePerformanceForRepeatedExecution()
        {
            // Arrange - 创建一个复杂流程
            var flow = new FunctionFlow
            {
                Id = "performance-test-flow",
                Version = 1,
                Data = new List<FlowData>(),
                Body = new List<FlowStep>()
            };

            // 添加多个步骤，混合需要和不需要引擎的情况
            for (int i = 0; i < 100; i++)
            {
                var needsScript = i % 10 == 0; // 每10个步骤中有1个需要脚本
                flow.Body.Add(new FlowStep
                {
                    Id = $"step{i}",
                    Function = "test-function",
                    Result = new List<FlowData>
                    {
                        new FlowData
                        {
                            Key = $"result{i}",
                            Type = "string",
                            Value = needsScript
                                ? new DataValue { Type = "script", ScriptValue = $"return 'result{i}';" }
                                : new DataValue { Type = "text", TextValue = $"result{i}" }
                        }
                    }
                });
            }

            // Act - 测试预分析的性能影响
            var loadStopwatch = Stopwatch.StartNew();
            var executor = new GCP.FunctionPool.FlowExecutor(flow);
            loadStopwatch.Stop();

            // Assert
            Assert.NotNull(executor);
            _output.WriteLine($"预分析100个步骤的流程耗时: {loadStopwatch.ElapsedMilliseconds}ms");

            // 预分析应该很快完成
            Assert.True(loadStopwatch.ElapsedMilliseconds < 100,
                $"预分析应该很快完成，实际耗时: {loadStopwatch.ElapsedMilliseconds}ms");
        }

        [Fact]
        public void LazyEngine_PerformanceComparison_WithoutScript()
        {
            // Arrange - 创建大量无脚本数据
            var flowData = new List<FlowData>();
            for (int i = 0; i < 1000; i++)
            {
                flowData.Add(new FlowData
                {
                    Key = $"item_{i}",
                    Type = "string",
                    Value = new DataValue { Type = "text", TextValue = $"Value {i}" }
                });
            }

            var globalData1 = new Dictionary<string, object>();
            var globalData2 = new Dictionary<string, object>();

            // Act - 测试懒加载版本（传入null引擎）
            var lazyStopwatch = Stopwatch.StartNew();
            FlowUtils.BindResult(flowData, globalData1, null, null, null, null);
            lazyStopwatch.Stop();

            // Act - 测试预创建引擎版本
            var eagerStopwatch = Stopwatch.StartNew();
            var engine = FlowUtils.GetEngine();
            FlowUtils.BindResult(flowData, globalData2, engine, null, null, null);
            eagerStopwatch.Stop();

            // Assert
            Assert.Equal(globalData1.Count, globalData2.Count);

            _output.WriteLine($"懒加载版本（无脚本）耗时: {lazyStopwatch.ElapsedMilliseconds}ms");
            _output.WriteLine($"预创建引擎版本（无脚本）耗时: {eagerStopwatch.ElapsedMilliseconds}ms");

            // 懒加载版本应该更快，因为没有创建不必要的引擎
            Assert.True(lazyStopwatch.ElapsedMilliseconds <= eagerStopwatch.ElapsedMilliseconds + 50,
                $"懒加载应该不慢于预创建引擎版本。懒加载: {lazyStopwatch.ElapsedMilliseconds}ms, 预创建: {eagerStopwatch.ElapsedMilliseconds}ms");
        }
    }
}
