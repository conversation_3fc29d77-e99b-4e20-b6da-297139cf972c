import{d as L,r as i,I as p,f as n,b as M,o as V,j as o,g as t,k as r,t as m,c as O,F as j,m as P,M as y}from"./index-CgN1WJ3_.js";const R=L({__name:"DialogForm",props:{visible:{type:Boolean,default:!1},data:Object},emits:["update:visible"],setup(C,{emit:k}){const f={name:"",status:"",description:"",type:"",mark:"",amount:0},w=[{label:"网关",value:"1"},{label:"人工智能",value:"2"},{label:"CVM",value:"3"}],v=C,u=i(!1),s=i({...f}),_=i(""),S=({validateResult:a,firstError:e})=>{e?(console.log("Errors: ",a),y.warning(e)):(y.success("提交成功"),u.value=!1)},T=()=>{u.value=!1,s.value={...f}},U=k;p(()=>u.value,a=>{U("update:visible",a)}),p(()=>v.visible,a=>{u.value=a}),p(()=>v.data,a=>{s.value=a});const B={name:[{required:!0,message:"请输入产品名称",type:"error"}]};return(a,e)=>{const b=n("t-input"),d=n("t-form-item"),c=n("t-radio"),D=n("t-radio-group"),E=n("t-option"),I=n("t-select"),N=n("t-textarea"),g=n("t-button"),x=n("t-form"),A=n("t-dialog");return V(),M(A,{visible:u.value,"onUpdate:visible":e[5]||(e[5]=l=>u.value=l),header:a.t("pages.listCard.create"),width:680,footer:!1},{body:o(()=>[t(x,{ref:"form",data:s.value,rules:B,"label-width":100,onSubmit:S},{default:o(()=>[t(d,{label:a.t("pages.listCard.productName"),name:"name"},{default:o(()=>[t(b,{modelValue:s.value.name,"onUpdate:modelValue":e[0]||(e[0]=l=>s.value.name=l),style:{width:"480px"}},null,8,["modelValue"])]),_:1},8,["label"]),t(d,{label:a.t("pages.listCard.productStatus"),name:"status"},{default:o(()=>[t(D,{modelValue:s.value.status,"onUpdate:modelValue":e[1]||(e[1]=l=>s.value.status=l)},{default:o(()=>[t(c,{value:"0"},{default:o(()=>[r(m(a.t("pages.listCard.productStatusEnum.off")),1)]),_:1}),t(c,{value:"1"},{default:o(()=>[r(m(a.t("pages.listCard.productStatusEnum.on")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label"]),t(d,{label:a.t("pages.listCard.productDescription"),name:"description"},{default:o(()=>[t(b,{modelValue:s.value.description,"onUpdate:modelValue":e[2]||(e[2]=l=>s.value.description=l),style:{width:"480px"}},null,8,["modelValue"])]),_:1},8,["label"]),t(d,{label:a.t("pages.listCard.productType"),name:"type"},{default:o(()=>[t(I,{modelValue:s.value.type,"onUpdate:modelValue":e[3]||(e[3]=l=>s.value.type=l),clearable:"",style:{width:"480px"}},{default:o(()=>[(V(),O(j,null,P(w,(l,F)=>t(E,{key:F,value:l.value,label:l.label},{default:o(()=>[r(m(l.label),1)]),_:2},1032,["value","label"])),64))]),_:1},8,["modelValue"])]),_:1},8,["label"]),t(d,{label:a.t("pages.listCard.productRemark"),name:"mark"},{default:o(()=>[t(N,{modelValue:_.value,"onUpdate:modelValue":e[4]||(e[4]=l=>_.value=l),style:{width:"480px"},name:"description"},null,8,["modelValue"])]),_:1},8,["label"]),t(d,{style:{float:"right"}},{default:o(()=>[t(g,{variant:"outline",onClick:T},{default:o(()=>e[6]||(e[6]=[r("取消")])),_:1,__:[6]}),t(g,{theme:"primary",type:"submit"},{default:o(()=>e[7]||(e[7]=[r("确定")])),_:1,__:[7]})]),_:1})]),_:1},8,["data"])]),_:1},8,["visible","header"])}}});export{R as _};
