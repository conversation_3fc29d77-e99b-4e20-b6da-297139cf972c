import{c as e,o as c,aS as n,d as p,af as i,f as l,g as o,a as d,u as f,j as u,_}from"./index-CgN1WJ3_.js";const F={xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"none",viewBox:"0 0 188 26"};function M(h,t){return c(),e("svg",F,t[0]||(t[0]=[n('<path fill="currentcolor" d="M77.73 8.344h2.94l-2.347 13.448H75.38zM78.6 3.1h3.022l-.548 3.022h-3.023zM32.577 6.47h4.36l.476-2.646H25.462l-.477 2.645h4.387l-2.701 15.322h3.185z"></path><path fill="currentcolor" fill-rule="evenodd" d="M51.405 11.428c.195-1.01.298-2.034.31-3.061 0-3.393-1.708-4.53-6.239-4.52h-6.238l-3.155 17.957h7.308c4.742 0 6.427-1.213 7.493-7.354zM45 6.47c2.562 0 3.537.333 3.537 2.37-.02.864-.112 1.726-.276 2.576l-.521 3.023c-.688 4.01-1.42 4.72-4.98 4.72h-3.023l2.21-12.689zM63.863 10.833q0 .526-.094 1.043l-.333 1.889c-.378 2.108-1.16 2.845-3.129 2.845h-5.225l-.121.737a7 7 0 0 0-.117 1.043c0 .828.426 1.02 1.802 1.02h4.768l-.974 2.396h-4.365c-3.08 0-4.156-.786-4.156-2.706q.018-.835.166-1.658l.926-5.313c.567-3.276 1.942-3.937 5.43-3.937h1.613c2.475-.015 3.809.646 3.809 2.64m-2.962.427c0-.57-.356-.714-1.448-.714h-1.685c-1.092 0-1.613.143-1.89 1.568l-.377 2.18h4.032a.874.874 0 0 0 1.02-.877l.26-1.541q.065-.306.08-.616z" clip-rule="evenodd"></path><path fill="currentcolor" d="M69.644 19.396h-5.623l.427 2.422h5.196c2.804 0 3.51-.332 3.937-2.8l.261-1.51q.198-.976.234-1.97c0-1.348-.687-1.753-2.773-1.753h-2.846c-.547 0-.71-.049-.71-.355q.01-.275.068-.544l.193-1.07c.166-.925.329-1.065 1.254-1.065h5.007l.994-2.396h-6c-2.68 0-3.39.295-3.866 3.023l-.189 1.07a11.6 11.6 0 0 0-.238 1.964c0 1.353.688 1.757 2.777 1.757h2.725c.547 0 .714.05.714.356a3.3 3.3 0 0 1-.072.544l-.216 1.258c-.166.926-.332 1.07-1.254 1.07"></path><path fill="currentcolor" fill-rule="evenodd" d="M93.264 13.823q.255-1.257.31-2.54c0-2.278-1.258-3.106-4.255-3.117h-1.636c-3.963 0-4.84.87-5.528 4.648l-.355 1.968q-.255 1.257-.31 2.54c0 2.274 1.247 3.105 4.27 3.105h3.4l-.165.926c-.356 2.086-.71 2.23-2.562 2.23h-4.886L81.974 26h5.124c2.728 0 4.126-.643 4.696-3.866zm-2.95-.027-.726 4.247-3.095-.011c-1.564 0-2.063-.19-2.063-1.21q.068-1.029.283-2.04l.36-1.968c.305-1.72.589-2.267 2.391-2.267h1.07c1.541 0 2.063.192 2.063 1.209-.04.686-.134 1.369-.284 2.04" clip-rule="evenodd"></path><path fill="currentcolor" d="M107.112 10.762c-.053.972-.179 1.94-.377 2.894l-1.425 8.135h-2.94l1.448-8.135q.192-.935.238-1.89c0-.83-.356-.997-1.47-.997h-3.465L97.2 21.776h-2.943l2.35-13.448h6.522c3.053.015 3.982.74 3.982 2.434M112.333 21.972h5.546c3.214 0 4.594-.405 5.166-3.619l.428-2.38c.095-.5.31-1.905.31-2.571 0-1.786-1.286-2.071-3.309-2.071h-3.047c-.667 0-1-.12-1-.524 0-.214.071-.69.19-1.31l.286-1.475c.238-1.214.429-1.452 1.524-1.452h5.784l1.095-2.619h-7.046c-2.976 0-3.904.667-4.404 3.523l-.452 2.524c-.119.69-.214 1.309-.214 1.833 0 1.5.642 2.237 2.57 2.237h3.547c.857 0 1.191.048 1.191.572 0 .261-.048.785-.215 1.618l-.238 1.238c-.309 1.667-.547 1.833-2.118 1.833h-6.071zM130.124 10.878h3.785l.428-2.404h-3.785l.714-4h-2.952l-.714 4h-1.785l-.405 2.404h1.762l-1.071 6.07c-.143.762-.262 1.596-.262 2.215 0 2.094 1.333 2.808 3.999 2.808h1.738l1.023-2.404h-1.618c-1.548 0-2.143-.024-2.143-.976 0-.38.095-1 .214-1.642z"></path><path fill="currentcolor" fill-rule="evenodd" d="M136.155 8.474h6.189c2.69 0 3.737.738 3.737 2.452 0 .571-.166 1.524-.309 2.333l-1.524 8.712h-7.141c-2.309 0-3.023-.833-3.023-2.404 0-.571.095-1.19.214-1.904l.143-.81c.571-3.19 1.618-3.38 4.856-3.38h3.571l.071-.428c.095-.5.167-.881.167-1.214 0-.714-.405-.953-1.762-.953h-4.785zm.904 10.38c0 .57.238.713.881.713h3.856l.667-3.809h-3.714c-.999 0-1.237.215-1.404 1.19l-.191 1.072c-.071.38-.095.643-.095.833" clip-rule="evenodd"></path><path fill="currentcolor" d="M146.66 21.972h2.976l1.595-8.975c.309-1.785.666-2.071 2.38-2.071h2.167l1.023-2.452h-3.285c-3.737 0-4.594.667-5.237 4.356zM165.098 10.878h-3.785l-1.071 6.07c-.119.643-.214 1.262-.214 1.643 0 .952.595.976 2.142.976h1.619l-1.024 2.404h-1.738c-2.666 0-3.999-.714-3.999-2.808 0-.62.119-1.453.262-2.214l1.071-6.07H156.6l.404-2.405h1.786l.714-4h2.952l-.714 4h3.785z"></path><path fill="currentcolor" fill-rule="evenodd" d="M173.343 8.307h-1.619c-3.499 0-4.88.667-5.451 3.952l-.928 5.332c-.096.572-.167 1.167-.167 1.667 0 1.928 1.095 2.714 4.19 2.714h4.38l.976-2.405h-4.785c-1.381 0-1.809-.19-1.809-1.023 0-.262.047-.643.119-1.048l.119-.738h5.237c1.976 0 2.761-.738 3.142-2.856l.333-1.88c.072-.382.096-.763.096-1.048 0-2-1.333-2.667-3.833-2.667m.786 3.714-.262 1.547c-.095.62-.357.881-1.024.881h-4.047l.381-2.19c.262-1.428.786-1.571 1.881-1.571h1.69c1.095 0 1.452.143 1.452.714 0 .167-.024.333-.071.62" clip-rule="evenodd"></path><path fill="currentcolor" d="M179.919 21.972h-2.976l1.619-9.142c.643-3.69 1.5-4.356 5.237-4.356h3.285l-1.024 2.452h-2.166c-1.714 0-2.071.286-2.38 2.071z"></path><path fill="#0064FF" d="M5.212 5.29H.482a.476.476 0 0 1-.477-.548L.784.378A.46.46 0 0 1 1.237 0H6.15z"></path><path fill="url(#a)" d="M8.53 16.512H3.229L5.212 5.297h5.302z"></path><path fill="#009BFF" d="M7.206 21.81H2.857a.48.48 0 0 1-.468-.556l.842-4.742h5.29l-.869 4.912a.46.46 0 0 1-.446.385"></path><path fill="url(#b)" d="M20.092 5.29H5.212L6.149 0h14.699a.476.476 0 0 1 .468.555l-.774 4.357a.454.454 0 0 1-.45.378"></path><defs><linearGradient id="a" x1="7.815" x2="7.926" y1="4.888" y2="15.44" gradientUnits="userSpaceOnUse"><stop stop-color="#009BFF"></stop><stop offset=".35" stop-color="#0081FE"></stop><stop offset=".75" stop-color="#006AFD"></stop><stop offset="1" stop-color="#0062FD"></stop></linearGradient><linearGradient id="b" x1="5.686" x2="20.378" y1="2.651" y2="5.413" gradientUnits="userSpaceOnUse"><stop offset=".03" stop-color="#E9FFFF"></stop><stop offset=".17" stop-color="#C4FAC9"></stop><stop offset=".33" stop-color="#A0F694"></stop><stop offset=".48" stop-color="#82F269"></stop><stop offset=".63" stop-color="#6AEF47"></stop><stop offset=".76" stop-color="#5AED2F"></stop><stop offset=".89" stop-color="#4FEB20"></stop><stop offset="1" stop-color="#4CEB1B"></stop></linearGradient></defs>',14)]))}const g={render:M},m={class:"login-header"},v={class:"operations-container"},z=p({__name:"Header",setup(h){const t=i(),s=()=>{t.updateConfig({showSettingPanel:!0})};return(x,q)=>{const a=l("t-icon"),r=l("t-button");return c(),e("header",m,[o(f(g),{class:"logo"}),d("div",v,[o(r,{theme:"default",shape:"square",variant:"text",onClick:s},{default:u(()=>[o(a,{name:"setting",class:"icon"})]),_:1})])])}}}),B=_(z,[["__scopeId","data-v-50c0cd33"]]);export{B as default};
