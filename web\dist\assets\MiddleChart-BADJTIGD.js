import{d as N,af as Y,r as x,D as L,h as O,W as w,ag as P,I as m,ah as k,ai as j,f as p,b as q,o as G,j as i,g as d,a as C,a1 as M,u as H,_ as J}from"./index-CgN1WJ3_.js";import{L as K}from"./date-r6Llbv4_.js";import{g as $,a as Q}from"./index-DHK-juMe.js";import{u as R,i as U,a as X,b as y}from"./installCanvasRenderer-akfsAsjg.js";import{i as Z,a as V,b as tt}from"./install-DYqkEeHz.js";import{i as et}from"./install-UPPBFq-o.js";import"./dayjs.min-DmQUmdwp.js";import"./charts-SY082_ji.js";import"./sectorHelper-Bt-evzC6.js";const at={class:"dashboard-chart-title-container"},nt=N({__name:"MiddleChart",setup(ot){R([Z,V,et,tt,U,X]);const b=t=>{let e;if(!t||t.length===0)return e=new Date,`${e.getFullYear()}-${e.getMonth()+1}`;e=new Date(t[0]);const s=new Date(t[1]),h=e.getMonth()+1>9?e.getMonth()+1:`0${e.getMonth()+1}`,u=s.getMonth()+1>9?s.getMonth()+1:`0${s.getMonth()+1}`;return`${e.getFullYear()}-${h}  至  ${s.getFullYear()}-${u}`},r=Y(),a=x(1),g=L(()=>r.chartColors);let l,o;const S=()=>{l||(l=document.getElementById("monitorContainer")),o=y(l),o.setOption($({...g.value}))};let _,n;const W=()=>{_||(_=document.getElementById("countContainer")),n=y(_),n.setOption(Q(g.value)),n.dispatchAction({type:"downplay",seriesIndex:0,dataIndex:-1}),n.dispatchAction({type:"highlight",seriesIndex:0,dataIndex:1}),n.dispatchAction({type:"showTip",seriesIndex:0,dataIndex:1})},v=()=>{S(),W()},c=()=>{document.documentElement.clientWidth>=1400&&document.documentElement.clientWidth<1920?a.value=Number((document.documentElement.clientWidth/2080).toFixed(2)):document.documentElement.clientWidth<1080?a.value=Number((document.documentElement.clientWidth/1080).toFixed(2)):a.value=1,o.resize({width:l.clientWidth,height:a.value*326}),n.resize({width:a.value*326,height:a.value*326})};O(()=>{v(),w(()=>{c()})});const{width:I,height:T}=P();m([I,T],()=>{c()}),k(()=>{E(),B(),D()});const f=x(b()),B=m(()=>r.brandTheme,()=>{j([o,n])}),D=m(()=>r.isSidebarCompact,()=>{r.isSidebarCompact?w(()=>{c()}):setTimeout(()=>{c()},180)}),E=m(()=>r.mode,()=>{[o,n].forEach(t=>{t.dispose()}),v()}),z=t=>{f.value=b(t),o.setOption($({dateTime:t,...g.value}))};return(t,e)=>{const s=p("t-date-range-picker"),h=p("t-card"),u=p("t-col"),A=p("t-row");return G(),q(A,{gutter:16,class:"row-container"},{default:i(()=>[d(u,{xs:12,xl:9},{default:i(()=>[d(h,{title:t.t("pages.dashboardBase.topPanel.analysis.title"),subtitle:f.value,class:"dashboard-chart-card",bordered:!1},{actions:i(()=>[C("div",at,[d(s,{class:"card-date-picker-container",theme:"primary",mode:"date","default-value":H(K),onChange:e[0]||(e[0]=F=>z(F))},null,8,["default-value"])])]),default:i(()=>[C("div",{id:"monitorContainer",class:"dashboard-chart-container",style:M({width:"100%",height:`${a.value*326}px`})},null,4)]),_:1},8,["title","subtitle"])]),_:1}),d(u,{xs:12,xl:3},{default:i(()=>[d(h,{title:t.t("pages.dashboardBase.topPanel.analysis.channels"),subtitle:f.value,class:"dashboard-chart-card",bordered:!1},{default:i(()=>[C("div",{id:"countContainer",class:"dashboard-chart-container",style:M({width:`${a.value*326}px`,height:`${a.value*326}px`,margin:"0 auto"})},null,4)]),_:1},8,["title","subtitle"])]),_:1})]),_:1})}}}),pt=J(nt,[["__scopeId","data-v-35bc3ab3"]]);export{pt as default};
