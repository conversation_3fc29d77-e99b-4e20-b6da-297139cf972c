import{C as _}from"./index-DQL6tnVY.js";import{d as b,r as s,f as r,c as m,o as f,a as t,g as e,j as h,u as c,s as x,_ as T}from"./index-CgN1WJ3_.js";import"./list-9iiWcExX.js";import"./index-Dp_lJtP-.js";import"./index-BCrtNi6h.js";const C=[{label:"深圳总部",value:0,children:[{label:"总办",value:"0-0"},{label:"市场部",value:"0-1",children:[{label:"采购1组",value:"0-1-0"},{label:"采购2组",value:"0-1-1"}]},{label:"技术部",value:"0-2"}]},{label:"北京总部",value:1,children:[{label:"总办",value:"1-0"},{label:"市场部",value:"1-1",children:[{label:"采购1组",value:"1-1-0"},{label:"采购2组",value:"1-1-1"}]}]},{label:"上海总部",value:2,children:[{label:"市场部",value:"2-0"},{label:"财务部",value:"2-1",children:[{label:"财务1组",value:"2-1-0"},{label:"财务2组",value:"2-1-1"}]}]},{label:"湖南",value:3},{label:"湖北",value:4}],V={class:"table-tree-container"},B={class:"list-tree-wrapper"},g={class:"list-tree-operator"},k={class:"list-tree-content"},E={name:"ListTree"},w=b({...E,setup(z){const o=s(),l=s(),d=["0","0-0","0-1","0-2","0-3","0-4"],i=()=>{o.value=a=>a.label.indexOf(l.value)>=0};return(a,n)=>{const u=r("t-input"),p=r("t-tree");return f(),m("div",V,[t("div",B,[t("div",g,[e(u,{modelValue:l.value,"onUpdate:modelValue":n[0]||(n[0]=v=>l.value=v),placeholder:a.t("pages.listTree.placeholder"),onChange:i},{"suffix-icon":h(()=>[e(c(x),{size:"var(--td-comp-size-xxxs)"})]),_:1},8,["modelValue","placeholder"]),e(p,{data:c(C),hover:"","expand-on-click-node":"","default-expanded":d,filter:o.value},null,8,["data","filter"])]),t("div",k,[e(_)])])])}}}),D=T(w,[["__scopeId","data-v-62b1edb1"]]);export{D as default};
