import{R as s}from"./index-BIadYJ_w.js";import{d as o,f as r,b as i,o as p,j as a,g as l,k as u,t as m}from"./index-CgN1WJ3_.js";const c={name:"ResultMaintenance"},b=o({...c,setup(f){return(t,e)=>{const n=r("t-button");return p(),i(s,{title:t.t("pages.result.maintenance.title"),tip:t.t("pages.result.maintenance.subtitle"),type:"maintenance"},{default:a(()=>[l(n,{theme:"primary",onClick:e[0]||(e[0]=()=>t.$router.push("/"))},{default:a(()=>[u(m(t.t("pages.result.maintenance.back")),1)]),_:1})]),_:1},8,["title","tip"])}}});export{b as default};
