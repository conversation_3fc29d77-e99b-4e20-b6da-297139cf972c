import{R as n}from"./index-BIadYJ_w.js";import{d as u,f as p,b as i,o as d,j as r,a as f,g as s,k as a,t as l}from"./index-CgN1WJ3_.js";const k={name:"ResultNetworkError"},_=u({...k,setup(m){return(t,e)=>{const o=p("t-button");return d(),i(n,{title:t.t("pages.result.networkError.title"),tip:t.t("pages.result.networkError.subtitle"),type:"wifi"},{default:r(()=>[f("div",null,[s(o,{theme:"default",onClick:e[0]||(e[0]=()=>t.$router.push("/"))},{default:r(()=>[a(l(t.t("pages.result.networkError.back")),1)]),_:1}),s(o,{onClick:e[1]||(e[1]=()=>t.$router.push("/"))},{default:r(()=>[a(l(t.t("pages.result.networkError.reload")),1)]),_:1})])]),_:1},8,["title","tip"])}}});export{_ as default};
