import{d as ee,af as te,l as t,r as g,D as w,h as ae,B as se,aR as ne,f as d,c as _,o as c,g as o,j as n,a as I,e as u,k as r,t as l,u as e,s as le,b as y,M as oe,_ as ie}from"./index-CgN1WJ3_.js";import{g as ce}from"./list-9iiWcExX.js";import{T as P}from"./index-Dp_lJtP-.js";import{C as A,a as k,b as v}from"./index-BCrtNi6h.js";const ue={class:"left-operation-container"},re={key:0,class:"selected-count"},de={class:"search-input"},pe={key:0},ge={key:1},_e={key:2},fe={key:0,class:"payment-col"},me={key:1,class:"payment-col"},he={name:"ListBase"},ye=ee({...he,setup(ve){const K=te(),D=[{colKey:"row-select",type:"multiple",width:64,fixed:"left"},{title:t("pages.listBase.contractName"),align:"left",width:320,colKey:"name",fixed:"left"},{title:t("pages.listBase.contractStatus"),colKey:"status",width:160},{title:t("pages.listBase.contractNum"),width:160,ellipsis:!0,colKey:"no"},{title:t("pages.listBase.contractType"),width:160,ellipsis:!0,colKey:"contractType"},{title:t("pages.listBase.contractPayType"),width:160,ellipsis:!0,colKey:"paymentType"},{title:t("pages.listBase.contractAmount"),width:160,ellipsis:!0,colKey:"amount"},{title:t("pages.listBase.operation"),align:"left",fixed:"right",width:160,colKey:"op"}],m=g([]),C=g({defaultPageSize:20,total:100,defaultCurrent:1}),S=g(""),B=g(!1),U=async()=>{B.value=!0;try{const{list:s}=await ce();m.value=s,C.value={...C.value,total:s.length}}catch(s){console.log(s)}finally{B.value=!1}},f=g(-1),V=w(()=>{if(f.value>-1){const{name:s}=m.value[f.value];return`删除后，${s}的所有合同信息将被清空，且无法恢复`}return""});ae(()=>{U()});const T=g(!1),p=g([1,2]),b=se(),x=()=>{f.value=-1},M=()=>{m.value.splice(f.value,1),C.value.total=m.value.length;const s=p.value.indexOf(f.value);s>-1&&p.value.splice(s,1),T.value=!1,oe.success("删除成功"),x()},R=()=>{x()},L="index",O=s=>{p.value=s},Y=(s,i)=>{console.log("分页变化",s,i)},$=(s,i)=>{console.log("统一Change",s,i)},G=()=>{b.push("/detail/base")},j=()=>{b.push("/form/base")},z=s=>{f.value=s.rowIndex,T.value=!0},F=w(()=>({offsetTop:K.isUseTabsRouter?48:0,container:`.${ne}-layout`}));return(s,i)=>{const E=d("t-button"),X=d("t-input"),H=d("t-row"),h=d("t-tag"),N=d("t-link"),q=d("t-space"),J=d("t-table"),Q=d("t-card"),W=d("t-dialog");return c(),_("div",null,[o(Q,{class:"list-card-container",bordered:!1},{default:n(()=>[o(H,{justify:"space-between"},{default:n(()=>[I("div",ue,[o(E,{onClick:j},{default:n(()=>[r(l(e(t)("pages.listBase.create")),1)]),_:1}),o(E,{variant:"base",theme:"default",disabled:!p.value.length},{default:n(()=>[r(l(e(t)("pages.listBase.export")),1)]),_:1},8,["disabled"]),p.value.length?(c(),_("p",re,l(e(t)("pages.listBase.select"))+" "+l(p.value.length)+" "+l(e(t)("pages.listBase.items")),1)):u("",!0)]),I("div",de,[o(X,{modelValue:S.value,"onUpdate:modelValue":i[0]||(i[0]=a=>S.value=a),placeholder:e(t)("pages.listBase.placeholder"),clearable:""},{"suffix-icon":n(()=>[o(e(le),{size:"16px"})]),_:1},8,["modelValue","placeholder"])])]),_:1}),o(J,{data:m.value,columns:D,"row-key":L,"vertical-align":"top",hover:!0,pagination:C.value,"selected-row-keys":p.value,loading:B.value,"header-affixed-top":F.value,onPageChange:Y,onChange:$,onSelectChange:i[2]||(i[2]=a=>O(a))},{status:n(({row:a})=>[a.status===e(v).FAIL?(c(),y(h,{key:0,theme:"danger",variant:"light"},{default:n(()=>[r(l(e(t)("pages.listBase.contractStatusEnum.fail")),1)]),_:1})):u("",!0),a.status===e(v).AUDIT_PENDING?(c(),y(h,{key:1,theme:"warning",variant:"light"},{default:n(()=>[r(l(e(t)("pages.listBase.contractStatusEnum.audit")),1)]),_:1})):u("",!0),a.status===e(v).EXEC_PENDING?(c(),y(h,{key:2,theme:"warning",variant:"light"},{default:n(()=>[r(l(e(t)("pages.listBase.contractStatusEnum.pending")),1)]),_:1})):u("",!0),a.status===e(v).EXECUTING?(c(),y(h,{key:3,theme:"success",variant:"light"},{default:n(()=>[r(l(e(t)("pages.listBase.contractStatusEnum.executing")),1)]),_:1})):u("",!0),a.status===e(v).FINISH?(c(),y(h,{key:4,theme:"success",variant:"light"},{default:n(()=>[r(l(e(t)("pages.listBase.contractStatusEnum.finish")),1)]),_:1})):u("",!0)]),contractType:n(({row:a})=>[a.contractType===e(k).MAIN?(c(),_("p",pe,l(e(t)("pages.listBase.contractStatusEnum.fail")),1)):u("",!0),a.contractType===e(k).SUB?(c(),_("p",ge,l(e(t)("pages.listBase.contractStatusEnum.audit")),1)):u("",!0),a.contractType===e(k).SUPPLEMENT?(c(),_("p",_e,l(e(t)("pages.listBase.contractStatusEnum.pending")),1)):u("",!0)]),paymentType:n(({row:a})=>[a.paymentType===e(A).PAYMENT?(c(),_("div",fe,[r(l(e(t)("pages.listBase.pay")),1),o(P,{class:"dashboard-item-trend",type:"up"})])):u("",!0),a.paymentType===e(A).RECEIPT?(c(),_("div",me,[r(l(e(t)("pages.listBase.receive")),1),o(P,{class:"dashboard-item-trend",type:"down"})])):u("",!0)]),op:n(a=>[o(q,null,{default:n(()=>[o(N,{theme:"primary",onClick:i[1]||(i[1]=Z=>G())},{default:n(()=>[r(l(e(t)("pages.listBase.detail")),1)]),_:1}),o(N,{theme:"danger",onClick:Z=>z(a)},{default:n(()=>[r(l(e(t)("pages.listBase.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:1},8,["data","pagination","selected-row-keys","loading","header-affixed-top"])]),_:1}),o(W,{visible:T.value,"onUpdate:visible":i[3]||(i[3]=a=>T.value=a),header:"确认删除当前所选合同？",body:V.value,"on-cancel":R,onConfirm:M},null,8,["visible","body"])])}}}),Se=ie(ye,[["__scopeId","data-v-39885cf6"]]);export{Se as default};
