import{l as t,aN as F,d as K,af as Q,D as z,h as X,W as Z,ag as ee,I as v,ah as te,ai as ae,f as h,c as T,o as b,g as o,j as n,F as P,m as M,u as x,b as N,a as y,t as L,k as W,E as B,_ as se}from"./index-CgN1WJ3_.js";import{P as oe}from"./index-DIXxPAlN.js";import{T as ie}from"./index-Dp_lJtP-.js";import{L as I}from"./date-r6Llbv4_.js";import{d as $}from"./dayjs.min-DmQUmdwp.js";import{g as e,a as re}from"./charts-SY082_ji.js";import{i as ne}from"./install-CHcVn6YI.js";import{u as le,i as de,a as ce,b as E}from"./installCanvasRenderer-akfsAsjg.js";import{b as pe,a as me,i as ue}from"./install-DYqkEeHz.js";import"./more-y8MHxVY_.js";import"./shop-Ca-zpMb4.js";import"./service-_rBmeUwI.js";import"./user-avatar-C4gpfUj0.js";import"./laptop-CtTBJH0D.js";const he=[{title:t("pages.dashboardDetail.topPanel.paneList.totalRequest"),number:"1126",upTrend:"10%"},{title:t("pages.dashboardDetail.topPanel.paneList.suppliers"),number:"13",downTrend:"13%"},{title:t("pages.dashboardDetail.topPanel.paneList.productCategory"),number:"4",upTrend:"10%"},{title:t("pages.dashboardDetail.topPanel.paneList.applicant"),number:90,downTrend:"44%",leftType:"icon-file-paste"},{title:t("pages.dashboardDetail.topPanel.paneList.completionRate"),number:80.5,upTrend:"70%"},{title:t("pages.dashboardDetail.topPanel.paneList.arrivalRate"),number:78,upTrend:"16%"}],ge=[{description:t("pages.dashboardDetail.sslDescription"),index:1,isSetup:!0,name:t("pages.dashboardDetail.ssl"),type:4},{description:t("pages.dashboardDetail.sslDescription"),index:1,isSetup:!0,name:t("pages.dashboardDetail.ssl"),type:4}];function O({dateTime:c=[],placeholderColor:i,borderColor:s}){const a=[],g=[],m=[];for(let r=0;r<40;r++){if(c.length>0){const S=(new Date(c[1]).getTime()-new Date(c[0]).getTime())/40,f=new Date(c[0]).getTime()+S*r;a.push($(f).format("MM-DD"))}else a.push($().subtract(40-r,"day").format("MM-DD"));g.push(e().toString()),m.push(e().toString())}return{color:F(),xAxis:{data:a,axisLabel:{color:i},splitLine:{show:!1},axisLine:{lineStyle:{color:s,width:1}}},yAxis:{type:"value",axisLabel:{color:i},nameTextStyle:{padding:[0,0,0,60]},axisTick:{show:!1,axisLine:{show:!1}},axisLine:{show:!1},splitLine:{lineStyle:{color:s}}},tooltip:{trigger:"item"},grid:{top:"5px",left:"25px",right:"5px",bottom:"60px"},legend:{left:"center",bottom:"0",orient:"horizontal",data:[t("pages.dashboardDetail.procurement.goods.massageMachine"),t("pages.dashboardDetail.procurement.goods.coffeeMachine")],itemHeight:8,itemWidth:8,textStyle:{fontSize:12,color:i}},series:[{name:t("pages.dashboardDetail.procurement.goods.massageMachine"),symbolSize:10,data:m.reverse(),type:"scatter"},{name:t("pages.dashboardDetail.procurement.goods.coffeeMachine"),symbolSize:10,data:g.concat(g.reverse()),type:"scatter"}]}}function R({dateTime:c=[],placeholderColor:i,borderColor:s}){let l=[];for(let a=1;a<7;a++)l.push(t(`pages.dashboardDetail.chart.week${a}`));return c.length>0&&(l=re(c,7)),{color:F(),grid:{top:"5%",right:"10px",left:"30px",bottom:"60px"},legend:{left:"center",bottom:"0",orient:"horizontal",data:[t("pages.dashboardDetail.procurement.goods.cup"),t("pages.dashboardDetail.procurement.goods.tea"),t("pages.dashboardDetail.procurement.goods.honey"),t("pages.dashboardDetail.procurement.goods.flour")],textStyle:{fontSize:12,color:i}},xAxis:{type:"category",data:l,boundaryGap:!1,axisLabel:{color:i},axisLine:{lineStyle:{color:s,width:1}}},yAxis:{type:"value",axisLabel:{color:i},splitLine:{lineStyle:{color:s}}},tooltip:{trigger:"item"},series:[{showSymbol:!0,symbol:"circle",symbolSize:8,name:t("pages.dashboardDetail.procurement.goods.cup"),stack:"总量",data:[e(),e(),e(),e(),e(),e(),e()],type:"line",itemStyle:{borderColor:s,borderWidth:1}},{showSymbol:!0,symbol:"circle",symbolSize:8,name:t("pages.dashboardDetail.procurement.goods.tea"),stack:"总量",data:[e(),e(),e(),e(),e(),e(),e()],type:"line",itemStyle:{borderColor:s,borderWidth:1}},{showSymbol:!0,symbol:"circle",symbolSize:8,name:t("pages.dashboardDetail.procurement.goods.honey"),stack:"总量",data:[e(),e(),e(),e(),e(),e(),e()],type:"line",itemStyle:{borderColor:s,borderWidth:1}},{showSymbol:!0,symbol:"circle",symbolSize:8,name:t("pages.dashboardDetail.procurement.goods.flour"),stack:"总量",data:[e(),e(),e(),e(),e(),e(),e()],type:"line",itemStyle:{borderColor:s,borderWidth:1}}]}}const be={class:"dashboard-panel-detail"},ye={class:"dashboard-list-card__number"},fe={class:"dashboard-list-card__text"},_e={class:"dashboard-list-card__text-left"},De={name:"DashboardDetail"},xe=K({...De,setup(c){le([pe,me,ue,de,ne,ce]);const i=Q(),s=z(()=>i.chartColors);let l,a;const g=()=>{l=document.getElementById("lineContainer"),a=E(l),a.setOption(R({...s.value}))};let m,r;const S=()=>{m=document.getElementById("scatterContainer"),r=E(m),r.setOption(O({...s.value}))},f=()=>{a==null||a.resize({width:l.clientWidth,height:l.clientHeight}),r==null||r.resize({width:m.clientWidth,height:m.clientHeight})},k=()=>{S(),g()};X(()=>{k(),Z(()=>{f()})});const{width:H,height:V}=ee();v([H,V],()=>{f()}),te(()=>{j(),q()});const j=v(()=>i.mode,()=>{k()}),q=v(()=>i.brandTheme,()=>{ae([a,r])}),G=()=>{r.setOption(O({...s.value}))},U=u=>{const p=z(()=>i.chartColors);a.setOption(R({dateTime:u,...p.value}))};return(u,p)=>{const Y=h("t-icon"),_=h("t-card"),w=h("t-col"),A=h("t-row"),C=h("t-date-range-picker"),J=h("t-button");return b(),T("div",be,[o(_,{title:u.t("pages.dashboardDetail.topPanel.title"),class:"dashboard-detail-card",bordered:!1},{default:n(()=>[o(A,{gutter:[16,16]},{default:n(()=>[(b(!0),T(P,null,M(x(he),(d,D)=>(b(),N(w,{key:D,xs:6,xl:3},{default:n(()=>[o(_,{class:"dashboard-list-card",description:d.title},{default:n(()=>[y("div",ye,L(d.number),1),y("div",fe,[y("div",_e,[W(L(u.t("pages.dashboardDetail.topPanel.quarter"))+" ",1),o(ie,{class:"icon",type:d.upTrend?"up":"down",describe:d.upTrend||d.downTrend},null,8,["type","describe"])]),o(Y,{name:"chevron-right"})])]),_:2},1032,["description"])]),_:2},1024))),128))]),_:1})]),_:1},8,["title"]),o(A,{gutter:[16,16],class:"row-margin"},{default:n(()=>[o(w,{xs:12,xl:9},{default:n(()=>[o(_,{class:"dashboard-detail-card",title:u.t("pages.dashboardDetail.procurement.title"),bordered:!1},{actions:n(()=>[o(C,{class:"card-date-picker-container","default-value":x(I),theme:"primary",mode:"date",style:{width:"248px"},onChange:p[0]||(p[0]=d=>U(d))},null,8,["default-value"])]),default:n(()=>[p[1]||(p[1]=y("div",{id:"lineContainer",style:{width:"100%",height:"416px"}},null,-1))]),_:1,__:[1]},8,["title"])]),_:1}),o(w,{xs:12,xl:3},{default:n(()=>[(b(!0),T(P,null,M(x(ge),(d,D)=>(b(),N(oe,{key:D,product:d,class:B({"row-margin":D!==0,"product-card":!0})},null,8,["product","class"]))),128))]),_:1})]),_:1}),o(_,{class:B(["dashboard-detail-card","row-margin"]),title:u.t("pages.dashboardDetail.satisfaction.title"),bordered:!1},{actions:n(()=>[o(C,{class:"card-date-picker-container","default-value":x(I),theme:"primary",mode:"date",style:{display:"inline-block","margin-right":"var(--td-comp-margin-s)",width:"248px"},onChange:G},null,8,["default-value"]),o(J,{class:"card-date-button"},{default:n(()=>[W(L(u.t("pages.dashboardDetail.satisfaction.export")),1)]),_:1})]),default:n(()=>[p[2]||(p[2]=y("div",{id:"scatterContainer",style:{width:"100%",height:"434px"}},null,-1))]),_:1,__:[2]},8,["title"])])}}}),Ie=se(xe,[["__scopeId","data-v-d9c4c3a3"]]);export{Ie as default};
