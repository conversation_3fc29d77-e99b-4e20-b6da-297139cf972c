import S from"./more-y8MHxVY_.js";import{d as x,f as n,b as d,o as s,j as e,g as a,u as c,k as m,t as u,p as B,a as l,e as r,aP as N,_ as V}from"./index-CgN1WJ3_.js";import w from"./shop-Ca-zpMb4.js";import D from"./service-_rBmeUwI.js";import P from"./user-avatar-C4gpfUj0.js";import j from"./laptop-CtTBJH0D.js";const A={class:"list-card-item_detail--name"},M={class:"list-card-item_detail--desc"},q=x({__name:"index",props:{product:{type:Object}},emits:["manage-product","delete-item"],setup(t,{emit:f}){const p=f,g=["A","B","C","D","E"],y=o=>{p("manage-product",o)},k=o=>{p("delete-item",o)};return(o,z)=>{const i=n("t-avatar"),h=n("t-tag"),v=n("t-avatar-group"),_=n("t-button"),b=n("t-dropdown"),C=n("t-card");return s(),d(C,{theme:"poster2",bordered:!1},{avatar:e(()=>[a(i,{size:"56px"},{icon:e(()=>[t.product.type===1?(s(),d(c(w),{key:0})):r("",!0),t.product.type===2?(s(),d(c(N),{key:1})):r("",!0),t.product.type===3?(s(),d(c(D),{key:2})):r("",!0),t.product.type===4?(s(),d(c(P),{key:3})):r("",!0),t.product.type===5?(s(),d(c(j),{key:4})):r("",!0)]),_:1})]),status:e(()=>[a(h,{theme:t.product.isSetup?"success":"default",disabled:!t.product.isSetup},{default:e(()=>[m(u(t.product.isSetup?o.t("components.isSetup.on"):o.t("components.isSetup.off")),1)]),_:1},8,["theme","disabled"])]),content:e(()=>[l("p",A,u(t.product.name),1),l("p",M,u(t.product.description),1)]),footer:e(()=>[a(v,{cascading:"left-up",max:2},{default:e(()=>[a(i,null,{default:e(()=>[m(u(g[t.product.type-1]),1)]),_:1}),a(i,null,{icon:e(()=>[a(c(B))]),_:1})]),_:1})]),actions:e(()=>[a(b,{disabled:!t.product.isSetup,trigger:"click",options:[{content:o.t("components.manage"),value:"manage",onClick:()=>y(t.product)},{content:o.t("components.delete"),value:"delete",onClick:()=>k(t.product)}]},{default:e(()=>[a(_,{theme:"default",disabled:!t.product.isSetup,shape:"square",variant:"text"},{default:e(()=>[a(c(S))]),_:1},8,["disabled"])]),_:1},8,["disabled","options"])]),_:1})}}}),H=V(q,[["__scopeId","data-v-4ecace77"]]);export{H as P};
