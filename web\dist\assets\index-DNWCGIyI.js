import{a as T}from"./list-9iiWcExX.js";import{P as $}from"./index-DIXxPAlN.js";import{_ as F}from"./DialogForm.vue_vue_type_script_setup_true_lang-CsX325Xa.js";import{d as j,r as n,D as E,h as q,f as s,c as d,o as i,a as p,g as l,e as C,j as v,k as G,t as H,b as y,u as J,s as K,F as z,m as O,M as Q,_ as R}from"./index-CgN1WJ3_.js";import"./more-y8MHxVY_.js";import"./shop-Ca-zpMb4.js";import"./service-_rBmeUwI.js";import"./user-avatar-C4gpfUj0.js";import"./laptop-CtTBJH0D.js";const W={class:"list-card-operation"},X={class:"search-input"},Y={class:"list-card-items"},Z={class:"list-card-pagination"},ee={key:1,class:"list-card-loading"},te={name:"ListCard"},ae=j({...te,setup(oe){const b={name:"",status:"",description:"",type:"0",mark:"",amount:0},t=n({current:1,pageSize:12,total:0}),r=n(void 0),m=n([]),g=n(!0),x=async()=>{try{const{list:e}=await T();m.value=e,t.value={...t.value,total:e.length}}catch(e){console.log(e)}finally{g.value=!1}},V=E(()=>r.value?`确认删除后${r.value.name}的所有产品信息将被清空, 且无法恢复`:"");q(()=>{x()});const u=n(!1),_=n(""),c=n(!1),f=n({...b}),k=e=>{t.value.pageSize=e,t.value.current=1},S=e=>{t.value.current=e},h=e=>{c.value=!0,r.value=e},D=()=>{const{index:e}=r.value;m.value.splice(e-1,1),c.value=!1,Q.success("删除成功")},P=()=>{r.value=void 0,f.value={...b}},I=e=>{u.value=!0,f.value={name:e.name,status:e!=null&&e.isSetup?"1":"0",description:e.description,type:e.type.toString(),mark:"",amount:0}};return(e,o)=>{const L=s("t-button"),B=s("t-input"),M=s("t-col"),N=s("t-row"),U=s("t-pagination"),w=s("t-loading"),A=s("t-dialog");return i(),d("div",null,[p("div",W,[l(L,{onClick:o[0]||(o[0]=a=>u.value=!0)},{default:v(()=>[G(H(e.t("pages.listCard.create")),1)]),_:1}),p("div",X,[l(B,{modelValue:_.value,"onUpdate:modelValue":o[1]||(o[1]=a=>_.value=a),placeholder:e.t("pages.listCard.placeholder"),clearable:""},{"suffix-icon":v(()=>[_.value===""?(i(),y(J(K),{key:0,size:"var(--td-comp-size-xxxs)"})):C("",!0)]),_:1},8,["modelValue","placeholder"])])]),l(F,{visible:u.value,"onUpdate:visible":o[2]||(o[2]=a=>u.value=a),data:f.value},null,8,["visible","data"]),t.value.total>0&&!g.value?(i(),d(z,{key:0},[p("div",Y,[l(N,{gutter:[16,16]},{default:v(()=>[(i(!0),d(z,null,O(m.value.slice(t.value.pageSize*(t.value.current-1),t.value.pageSize*t.value.current),a=>(i(),y(M,{key:a.index,lg:4,xs:6,xl:3},{default:v(()=>[l($,{class:"list-card-item",product:a,onDeleteItem:h,onManageProduct:I},null,8,["product"])]),_:2},1024))),128))]),_:1})]),p("div",Z,[l(U,{modelValue:t.value.current,"onUpdate:modelValue":o[3]||(o[3]=a=>t.value.current=a),"page-size":t.value.pageSize,"onUpdate:pageSize":o[4]||(o[4]=a=>t.value.pageSize=a),total:t.value.total,"page-size-options":[12,24,36],onPageSizeChange:k,onCurrentChange:S},null,8,["modelValue","page-size","total"])])],64)):g.value?(i(),d("div",ee,[l(w,{size:"large",text:"加载数据中..."})])):C("",!0),l(A,{visible:c.value,"onUpdate:visible":o[5]||(o[5]=a=>c.value=a),header:"确认删除所选产品？",body:V.value,"on-cancel":P,onConfirm:D},null,8,["visible","body"])])}}}),ve=R(ae,[["__scopeId","data-v-b2ff9b4e"]]);export{ve as default};
