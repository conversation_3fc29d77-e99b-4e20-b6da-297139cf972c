import{d as oe,af as ae,B as ne,l as e,r as T,D as V,h as le,aR as se,f as m,c as f,o as u,g as a,a as ce,j as o,u as t,k as i,t as c,e as p,b as h,M as me,_ as ue}from"./index-CgN1WJ3_.js";import{g as ie}from"./list-9iiWcExX.js";import{T as B}from"./index-Dp_lJtP-.js";import{b as d,a as b,C as D}from"./index-BCrtNi6h.js";const re={class:"list-common-table"},pe={class:"table-container"},de={key:0},_e={key:1},Te={key:2},fe={key:0,class:"payment-col"},be={key:1,class:"payment-col"},ye="index",ve="top",ge=!0,he=oe({__name:"index",setup(Ee){const w=ae(),M=ne(),O=[{value:d.FAIL,label:e("components.commonTable.contractStatusEnum.fail")},{value:d.AUDIT_PENDING,label:e("components.commonTable.contractStatusEnum.audit")},{value:d.EXEC_PENDING,label:e("components.commonTable.contractStatusEnum.pending")},{value:d.EXECUTING,label:e("components.commonTable.contractStatusEnum.executing")},{value:d.FINISH,label:e("components.commonTable.contractStatusEnum.finish")}],R=[{value:b.MAIN,label:e("components.commonTable.contractTypeEnum.main")},{value:b.SUB,label:e("components.commonTable.contractTypeEnum.sub")},{value:b.SUPPLEMENT,label:e("components.commonTable.contractTypeEnum.supplement")}],K=[{title:e("components.commonTable.contractName"),fixed:"left",width:280,ellipsis:!0,align:"left",colKey:"name"},{title:e("components.commonTable.contractStatus"),colKey:"status",width:160},{title:e("components.commonTable.contractNum"),width:160,ellipsis:!0,colKey:"no"},{title:e("components.commonTable.contractType"),width:160,ellipsis:!0,colKey:"contractType"},{title:e("components.commonTable.contractPayType"),width:160,ellipsis:!0,colKey:"paymentType"},{title:e("components.commonTable.contractAmount"),width:160,ellipsis:!0,colKey:"amount"},{align:"left",fixed:"right",width:160,colKey:"op",title:e("components.commonTable.operation")}],r=T({...{name:"",no:"",type:""}}),E=T({defaultPageSize:20,total:100,defaultCurrent:1}),C=T(!1),y=T([]),S=T(!1),L=async()=>{S.value=!0;try{const{list:l}=await ie();y.value=l,E.value={...E.value,total:l.length}}catch(l){console.log(l)}finally{S.value=!1}},v=T(-1),F=V(()=>{if(v.value>-1){const{name:l}=y.value[v.value];return`删除后，${l}的所有合同信息将被清空，且无法恢复`}return""}),P=()=>{v.value=-1},G=()=>{y.value.splice(v.value,1),E.value.total=y.value.length,C.value=!1,me.success("删除成功"),P()},Y=()=>{P()};le(()=>{L()});const X=l=>{v.value=l.row.rowIndex,C.value=!0},$=l=>{console.log(l)},H=()=>{M.push("/detail/base")},W=l=>{console.log(l),console.log(r.value)},j=(l,s)=>{console.log("分页变化",l,s)},q=(l,s)=>{console.log("统一Change",l,s)},z=V(()=>({offsetTop:w.isUseTabsRouter?48:0,container:`.${se}-layout`}));return(l,s)=>{const k=m("t-input"),N=m("t-form-item"),_=m("t-col"),I=m("t-select"),x=m("t-row"),A=m("t-button"),J=m("t-form"),g=m("t-tag"),U=m("t-link"),Q=m("t-space"),Z=m("t-table"),ee=m("t-dialog");return u(),f("div",re,[a(J,{ref:"form",data:r.value,"label-width":80,colon:"",onReset:$,onSubmit:W},{default:o(()=>[a(x,null,{default:o(()=>[a(_,{span:10},{default:o(()=>[a(x,{gutter:[24,24]},{default:o(()=>[a(_,{span:4},{default:o(()=>[a(N,{label:t(e)("components.commonTable.contractName"),name:"name"},{default:o(()=>[a(k,{modelValue:r.value.name,"onUpdate:modelValue":s[0]||(s[0]=n=>r.value.name=n),class:"form-item-content",type:"search",placeholder:t(e)("components.commonTable.contractNamePlaceholder"),style:{minWidth:"134px"}},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1}),a(_,{span:4},{default:o(()=>[a(N,{label:t(e)("components.commonTable.contractStatus"),name:"status"},{default:o(()=>[a(I,{modelValue:r.value.status,"onUpdate:modelValue":s[1]||(s[1]=n=>r.value.status=n),class:"form-item-content",options:O,placeholder:t(e)("components.commonTable.contractStatusPlaceholder"),clearable:""},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1}),a(_,{span:4},{default:o(()=>[a(N,{label:t(e)("components.commonTable.contractNum"),name:"no"},{default:o(()=>[a(k,{modelValue:r.value.no,"onUpdate:modelValue":s[2]||(s[2]=n=>r.value.no=n),class:"form-item-content",placeholder:t(e)("components.commonTable.contractNumPlaceholder"),style:{minWidth:"134px"}},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1}),a(_,{span:4},{default:o(()=>[a(N,{label:t(e)("components.commonTable.contractType"),name:"type"},{default:o(()=>[a(I,{modelValue:r.value.type,"onUpdate:modelValue":s[3]||(s[3]=n=>r.value.type=n),style:{display:"inline-block"},class:"form-item-content",options:R,placeholder:t(e)("components.commonTable.contractTypePlaceholder"),clearable:""},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1})]),_:1})]),_:1}),a(_,{span:2,class:"operation-container"},{default:o(()=>[a(A,{theme:"primary",type:"submit",style:{marginLeft:"var(--td-comp-margin-s)"}},{default:o(()=>[i(c(t(e)("components.commonTable.query")),1)]),_:1}),a(A,{type:"reset",variant:"base",theme:"default"},{default:o(()=>[i(c(t(e)("components.commonTable.reset")),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["data"]),ce("div",pe,[a(Z,{data:y.value,columns:K,"row-key":ye,"vertical-align":ve,hover:ge,pagination:E.value,loading:S.value,"header-affixed-top":z.value,onPageChange:j,onChange:q},{status:o(({row:n})=>[n.status===t(d).FAIL?(u(),h(g,{key:0,theme:"danger",variant:"light"},{default:o(()=>[i(c(t(e)("components.commonTable.contractStatusEnum.fail")),1)]),_:1})):p("",!0),n.status===t(d).AUDIT_PENDING?(u(),h(g,{key:1,theme:"warning",variant:"light"},{default:o(()=>[i(c(t(e)("components.commonTable.contractStatusEnum.audit")),1)]),_:1})):p("",!0),n.status===t(d).EXEC_PENDING?(u(),h(g,{key:2,theme:"warning",variant:"light"},{default:o(()=>[i(c(t(e)("components.commonTable.contractStatusEnum.pending")),1)]),_:1})):p("",!0),n.status===t(d).EXECUTING?(u(),h(g,{key:3,theme:"success",variant:"light"},{default:o(()=>[i(c(t(e)("components.commonTable.contractStatusEnum.executing")),1)]),_:1})):p("",!0),n.status===t(d).FINISH?(u(),h(g,{key:4,theme:"success",variant:"light"},{default:o(()=>[i(c(t(e)("components.commonTable.contractStatusEnum.finish")),1)]),_:1})):p("",!0)]),contractType:o(({row:n})=>[n.contractType===t(b).MAIN?(u(),f("p",de,c(t(e)("pages.listBase.contractStatusEnum.fail")),1)):p("",!0),n.contractType===t(b).SUB?(u(),f("p",_e,c(t(e)("pages.listBase.contractStatusEnum.audit")),1)):p("",!0),n.contractType===t(b).SUPPLEMENT?(u(),f("p",Te,c(t(e)("pages.listBase.contractStatusEnum.pending")),1)):p("",!0)]),paymentType:o(({row:n})=>[n.paymentType===t(D).PAYMENT?(u(),f("div",fe,[i(c(t(e)("pages.listBase.pay")),1),a(B,{class:"dashboard-item-trend",type:"up"})])):p("",!0),n.paymentType===t(D).RECEIPT?(u(),f("div",be,[i(c(t(e)("pages.listBase.receive")),1),a(B,{class:"dashboard-item-trend",type:"down"})])):p("",!0)]),op:o(n=>[a(Q,null,{default:o(()=>[a(U,{theme:"primary",onClick:s[4]||(s[4]=te=>H())},{default:o(()=>[i(c(t(e)("pages.listBase.detail")),1)]),_:1}),a(U,{theme:"danger",onClick:te=>X(n)},{default:o(()=>[i(c(t(e)("pages.listBase.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:1},8,["data","pagination","loading","header-affixed-top"]),a(ee,{visible:C.value,"onUpdate:visible":s[5]||(s[5]=n=>C.value=n),header:"确认删除当前所选合同？",body:F.value,"on-cancel":Y,onConfirm:G},null,8,["visible","body"])])])}}}),Ie=ue(he,[["__scopeId","data-v-ec46839e"]]);export{Ie as C};
