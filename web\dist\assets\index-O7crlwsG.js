import{a as Q}from"./detail-DxTPHQZc.js";import{aN as v,l as e,d as Z,af as ee,D as te,r as T,h as N,X as ae,I as le,ai as oe,f as r,c as A,o as k,g as l,j as t,u as p,a as m,k as _,t as y,b as ne,e as ie,F as se,m as re,E as de,_ as pe}from"./index-CgN1WJ3_.js";import{a as ce,g as i}from"./charts-SY082_ji.js";import{j as ue,h as me}from"./install-eIN7xI_r.js";import{u as ye,i as ge,a as he,b as I}from"./installCanvasRenderer-akfsAsjg.js";import{i as _e,b as ve,a as fe}from"./install-DYqkEeHz.js";import{i as be}from"./install-Ps5eJEQM.js";import"./dayjs.min-DmQUmdwp.js";import"./sectorHelper-Bt-evzC6.js";const xe=[{name:"集群名",value:"helloworld",type:null},{name:"集群ID",value:"cls - 2ntelvxw",type:{key:"color",value:"blue"}},{name:"状态",value:"运行中",type:{key:"color",value:"green"}},{name:"K8S版本",value:"1.7.8",type:null},{name:"配置",value:"6.73 核 10.30 GB",type:null},{name:"所在地域",value:"广州",type:null},{name:"新增资源所属项目",value:"默认项目",type:null},{name:"节点数量",value:"4 个",type:null},{name:"节点网络",value:"vpc - 5frmkm1x",type:{key:"color",value:"blue"}},{name:"容器网络",value:"********** / 16",type:null},{name:"集群凭证",value:"显示凭证",type:{key:"color",value:"blue"}},{name:"创建/更新",value:"2018-05-31 22:11:44 2018-05-31 22:11:44",type:{key:"contractAnnex",value:"pdf"}},{name:"描述",value:"istio_test",type:null}];function W({dateTime:f=[],placeholderColor:d,borderColor:c}){let s=["00:00","02:00","04:00","06:00"];return f.length>0&&(s=ce(f,7)),{color:v(),tooltip:{trigger:"item"},grid:{top:"10px",left:"0",right:"20px",bottom:"36px",containLabel:!0},xAxis:{type:"category",data:s,boundaryGap:!1,axisLabel:{color:d},axisLine:{lineStyle:{color:c,width:1}}},yAxis:{type:"value",axisLabel:{color:d},splitLine:{lineStyle:{color:c}}},legend:{data:[e("pages.detailDeploy.deployTrend.thisMonth"),e("pages.detailDeploy.deployTrend.lastMonth")],icon:"circle",bottom:"0",itemGap:48,itemHeight:8,itemWidth:8,textStyle:{fontSize:12,color:d}},series:[{name:e("pages.detailDeploy.deployTrend.lastMonth"),data:[i(),i(),i(),i(),i(),i(),i()],type:"line",smooth:!0,color:v()[0],showSymbol:!0,symbol:"circle",symbolSize:8,areaStyle:{opacity:.1}},{name:e("pages.detailDeploy.deployTrend.thisMonth"),data:[i(),i(),i(),i(),i(),i(),i()],type:"line",smooth:!0,showSymbol:!0,symbol:"circle",symbolSize:8,color:v()[1]}]}}const E=[100,120,140,160,180,200,210];function V({isMonth:f=!1,placeholderColor:d,borderColor:c}){let s=E.concat([]),u=E.concat([]);f&&(s=s.reverse(),u=u.reverse());const h=[];for(let g=1;g<7;g++)h.push(e(`pages.detailDeploy.deployTrend.week${g}`));return{color:v(),tooltip:{trigger:"item"},grid:{top:"10px",left:"0",right:"0",bottom:"36px",containLabel:!0},xAxis:[{type:"category",data:h,axisTick:{alignWithLabel:!0},axisLabel:{color:d},axisLine:{lineStyle:{color:c,width:1}}}],yAxis:[{type:"value",axisLabel:{color:d},splitLine:{lineStyle:{color:c}}}],legend:{data:[e("pages.detailDeploy.deployTrend.lastYear"),e("pages.detailDeploy.deployTrend.thisYear")],bottom:"0",icon:"rect",itemGap:48,itemHeight:4,itemWidth:12,textStyle:{fontSize:12,color:d}},series:[{name:e("pages.detailDeploy.deployTrend.lastYear"),type:"bar",barWidth:"30%",data:s,itemStyle:{color:"#BCC4D0"}},{name:e("pages.detailDeploy.deployTrend.thisYear"),type:"bar",barWidth:"30%",data:u,itemStyle:{color:g=>g.value>=200?v()[1]:v()[0]}}]}}const De={class:"detail-deploy"},Ce={class:"dialog-info-block"},Le={class:"dialog-info-block"},ke={name:"DetailDeploy"},we=Z({...ke,setup(f){const d=[{width:"280",ellipsis:!0,colKey:"name",title:e("pages.detailDeploy.projectList.table.name"),sorter:(a,o)=>a.name.substr(10)-o.name.substr(10)},{width:"280",ellipsis:!0,title:e("pages.detailDeploy.projectList.table.admin"),colKey:"adminName"},{width:"280",className:"test",ellipsis:!0,colKey:"updateTime",title:e("pages.detailDeploy.projectList.table.createTime"),sorter:(a,o)=>Date.parse(a.updateTime)-Date.parse(o.updateTime)},{align:"left",width:"200",className:"test2",ellipsis:!0,colKey:"op",fixed:"right",title:e("pages.detailDeploy.projectList.table.operation")}];ye([ue,me,_e,ve,fe,be,ge,he]);const c=ee(),s=te(()=>c.chartColors),u=T([]),h=T({defaultPageSize:10,total:100,defaultCurrent:1}),g=async()=>{try{const{list:a}=await Q();u.value=a,h.value={...h.value,total:a.length}}catch(a){console.log(a)}},D=T(!1);let C,b;N(()=>{C=document.getElementById("monitorContainer"),b=I(C),b.setOption(W({...s.value})),setInterval(()=>{b.setOption(W({...s.value}))},3e3)});let L,x;N(()=>{L=document.getElementById("dataContainer"),x=I(L),x.setOption(V({...s.value}))});const j=()=>{b.resize({width:C.clientWidth,height:C.clientHeight}),x.resize({width:L.clientWidth,height:L.clientHeight})};ae(()=>{window.removeEventListener("resize",j)});const Y=()=>{x.setOption(V({...s.value}))};N(()=>{g(),window.addEventListener("resize",j,!1)}),le(()=>c.brandTheme,()=>{oe([b,x])});const M=a=>{console.log(a)},O=(a,o)=>{console.log("统一Change",a,o)},K=()=>{D.value=!0},G=()=>{D.value=!1},H=a=>{u.value.splice(a.rowIndex,1)};return(a,o)=>{const w=r("t-card"),z=r("t-col"),B=r("t-radio-button"),P=r("t-radio-group"),F=r("t-row"),U=r("t-tag"),$=r("t-link"),R=r("t-space"),X=r("t-icon"),q=r("t-table"),J=r("t-dialog");return k(),A("div",De,[l(F,{gutter:16},{default:t(()=>[l(z,{lg:6,xs:12},{default:t(()=>[l(w,{title:p(e)("pages.detailDeploy.deployTrend.title"),bordered:!1},{default:t(()=>o[2]||(o[2]=[m("div",{class:"deploy-panel-left"},[m("div",{id:"monitorContainer",style:{width:"100%",height:"265px"}})],-1)])),_:1,__:[2]},8,["title"])]),_:1}),l(z,{lg:6,xs:12},{default:t(()=>[l(w,{title:p(e)("pages.detailDeploy.deployTrend.warning"),bordered:!1},{actions:t(()=>[l(P,{"default-value":"dateVal",onChange:Y},{default:t(()=>[l(B,{value:"dateVal"},{default:t(()=>[_(y(p(e)("pages.detailDeploy.deployTrend.thisWeek")),1)]),_:1}),l(B,{value:"monthVal"},{default:t(()=>[_(y(p(e)("pages.detailDeploy.deployTrend.thisMonth")),1)]),_:1})]),_:1})]),default:t(()=>[o[3]||(o[3]=m("div",{id:"dataContainer",style:{width:"100%",height:"265px"}},null,-1))]),_:1,__:[3]},8,["title"])]),_:1})]),_:1}),l(w,{title:p(e)("pages.detailDeploy.projectList.title"),class:"container-base-margin-top",bordered:!1},{default:t(()=>[l(q,{columns:d,data:u.value,pagination:h.value,hover:!0,stripe:!0,"row-key":"index",onSortChange:M,onChange:O},{adminName:t(({row:n})=>[m("span",null,[_(y(n.adminName)+" ",1),n.adminPhone?(k(),ne(U,{key:0,size:"small"},{default:t(()=>[_(y(n.adminPhone),1)]),_:2},1024)):ie("",!0)])]),op:t(n=>[l(R,null,{default:t(()=>[l($,{theme:"primary",onClick:o[0]||(o[0]=S=>K())},{default:t(()=>[_(y(p(e)("pages.detailDeploy.projectList.table.manage")),1)]),_:1}),l($,{theme:"danger",onClick:S=>H(n)},{default:t(()=>[_(y(p(e)("pages.detailDeploy.projectList.table.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),"op-column":t(()=>[l(X,{name:"descending-order"})]),_:1},8,["data","pagination"])]),_:1},8,["title"]),l(J,{visible:D.value,"onUpdate:visible":o[1]||(o[1]=n=>D.value=n),header:p(e)("pages.detailDeploy.projectList.dialog.title"),onConfirm:G},{body:t(()=>[m("div",Ce,[m("div",Le,[(k(!0),A(se,null,re(p(xe),(n,S)=>(k(),A("div",{key:S,class:"info-item"},[m("h1",null,y(n.name),1),m("span",{class:de({green:n.type&&n.type.value==="green",blue:n.type&&n.type.value==="blue"})},y(n.value),3)]))),128))])])]),_:1},8,["visible","header"])])}}}),We=pe(we,[["__scopeId","data-v-29484036"]]);export{We as default};
