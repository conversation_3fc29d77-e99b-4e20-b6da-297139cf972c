import{d as y,l as t,f as c,c as o,o as p,g as n,j as r,a as s,F as g,m as v,t as i,E as f,e as h,k as B,u as a,_ as b}from"./index-CgN1WJ3_.js";const k={class:"detail-base"},x={class:"info-block"},D={key:0},C={name:"DetailBase"},N=y({...C,setup(A){const d=[{name:t("constants.contract.name"),value:"总部办公用品采购项目",type:null},{name:t("constants.contract.status"),value:"履行中",type:{key:"contractStatus",value:"inProgress"}},{name:t("constants.contract.num"),value:"BH00010",type:null},{name:t("constants.contract.type"),value:t("constants.contract.typeOptions.main"),type:null},{name:t("constants.contract.payType"),value:t("constants.contract.pay"),type:null},{name:t("constants.contract.amount"),value:"¥ 5,000,000",type:null},{name:t("constants.contract.company"),value:"腾讯科技（深圳）有限公司",type:null},{name:t("constants.contract.employee"),value:"欧尚",type:null},{name:t("constants.contract.signDate"),value:"2020-12-20",type:null},{name:t("constants.contract.effectiveDate"),value:"2021-01-20",type:null},{name:t("constants.contract.endDate"),value:"2022-12-20",type:null},{name:t("constants.contract.attachment"),value:"总部办公用品采购项目合同.pdf",type:{key:"contractAnnex",value:"pdf"}},{name:t("constants.contract.createDate"),value:"2020-12-22 10:00:00",type:null}];return(S,V)=>{const u=c("t-card"),l=c("t-step-item"),_=c("t-steps");return p(),o("div",k,[n(u,{title:a(t)("pages.detailBase.baseInfo.title"),bordered:!1},{default:r(()=>[s("div",x,[(p(),o(g,null,v(d,(e,m)=>s("div",{key:m,class:"info-item"},[s("h1",null,i(e.name),1),s("span",{class:f({inProgress:e.type&&e.type.value==="inProgress",pdf:e.type&&e.type.value==="pdf"})},[e.type&&e.type.key==="contractStatus"?(p(),o("i",D)):h("",!0),B(" "+i(e.value),1)],2)])),64))])]),_:1},8,["title"]),n(u,{title:a(t)("pages.detailBase.changelog.title"),class:"container-base-margin-top",bordered:!1},{default:r(()=>[n(_,{class:"detail-base-info-steps",layout:"vertical",theme:"dot",current:1},{default:r(()=>[n(l,{title:a(t)("pages.detailBase.changelog.step1.title"),content:a(t)("pages.detailBase.changelog.step1.subtitle")},null,8,["title","content"]),n(l,{title:a(t)("pages.detailBase.changelog.step2.title"),content:a(t)("pages.detailBase.changelog.step2.subtitle")},null,8,["title","content"]),n(l,{title:a(t)("pages.detailBase.changelog.step3.title"),content:a(t)("pages.detailBase.changelog.step3.desc")},null,8,["title","content"])]),_:1})]),_:1},8,["title"])])}}}),F=b(N,[["__scopeId","data-v-9b32e28b"]]);export{F as default};
