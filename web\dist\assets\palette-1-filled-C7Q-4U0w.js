import{d as C,D as a,aa as d,ab as O,ac as y}from"./index-CgN1WJ3_.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M7.11111 6.9375C7.11111 4.22234 9.28827 2 12 2C14.7117 2 16.8889 4.22234 16.8889 6.9375C16.8889 9.65266 14.7117 11.875 12 11.875C9.28827 11.875 7.11111 9.65266 7.11111 6.9375ZM1 17.0625C1 14.3473 3.17716 12.125 5.88889 12.125C8.60062 12.125 10.7778 14.3473 10.7778 17.0625C10.7778 19.7777 8.60062 22 5.88889 22C3.17716 22 1 19.7777 1 17.0625ZM13.2222 17.0625C13.2222 14.3473 15.3994 12.125 18.1111 12.125C20.8228 12.125 23 14.3473 23 17.0625C23 19.7777 20.8228 22 18.1111 22C15.3994 22 13.2222 19.7777 13.2222 17.0625Z"}}]},g=C({name:"Palette1FilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=d(r),p=a(()=>["t-icon","t-icon-palette-1-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>O(m,f.value)}});export{g as default};
