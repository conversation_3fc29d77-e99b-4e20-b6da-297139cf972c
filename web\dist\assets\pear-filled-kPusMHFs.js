import{d as v,D as a,aa as d,ab as O,ac as y}from"./index-CgN1WJ3_.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M21.3231 2.53408L20.8758 3.4285C20.5175 4.14524 20.1201 4.80387 19.6513 5.45131C20.5289 6.55209 21.009 7.80482 20.9642 9.14574C20.9107 10.7502 20.1117 12.3024 18.7071 13.707C18.5949 13.8031 18.4795 13.8952 18.3718 13.9965C18.2191 14.1401 18.0117 14.3569 17.8043 14.6397C17.388 15.2074 16.9978 16.0036 17 16.9977C17.0038 18.6839 16.7101 20.1133 15.5025 21.3209C13.1038 23.7196 9.19547 23.6886 6.82782 21.3209C6.13578 20.6289 5.43051 19.9503 5.12332 18.8766C4.04961 18.5694 3.37101 17.8641 2.67899 17.1721C0.311338 14.8044 0.280321 10.8961 2.67899 8.49742C3.88658 7.28983 5.31609 6.99612 7.00227 6.9999C7.99629 7.00213 8.79252 6.61196 9.36026 6.19562C9.64304 5.98826 9.85983 5.78079 10.0034 5.62812C10.1047 5.52043 10.1968 5.40503 10.2929 5.29279C11.6784 3.90735 13.1823 3.06416 14.751 2.96422C15.9922 2.88515 17.1531 3.27853 18.1961 4.0454C18.5344 3.55524 18.8233 3.06141 19.087 2.53408L19.5342 1.63965L21.3231 2.53408Z"}}]},g=v({name:"PearFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=d(t),p=a(()=>["t-icon","t-icon-pear-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),C=a(()=>({class:p.value,style:u.value,onClick:f=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:f})}}));return()=>O(m,C.value)}});export{g as default};
