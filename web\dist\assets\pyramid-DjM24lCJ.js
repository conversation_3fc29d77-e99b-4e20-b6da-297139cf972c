import{d as y,D as a,aa as d,ab as O,ac as m}from"./index-CgN1WJ3_.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M10.9999 2.96045L15.4395 10.8531L16.4999 8.92514L23.6912 22.0002H0.290039L10.9999 2.96045ZM13.2275 11.0002L10.9999 7.03991L8.77223 11.0002H13.2275ZM7.64723 13.0002L3.70973 20.0002H9.99989V13.0002H7.64723ZM11.9999 13.0002V17.107L14.2586 13.0002H11.9999ZM12.6912 20.0002H15.4999V17.0002H14.3412L12.6912 20.0002ZM17.4999 20.0002H20.3086L18.6586 17.0002H17.4999V20.0002ZM17.5586 15.0002L16.4999 13.0752L15.4412 15.0002H17.5586Z"}}]},L=y({name:"PyramidIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=d(t),p=a(()=>["t-icon","t-icon-pyramid",o.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:f})}}));return()=>O(b,v.value)}});export{L as default};
