import{d as v,D as n,aa as y,ab as f,ac as d}from"./index-CgN1WJ3_.js";function l(e,a){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);a&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var a=1;a<arguments.length;a++){var r=arguments[a]!=null?arguments[a]:{};a%2?l(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M6 2H18V4H17V8H19V11H20V14H21V17H22V23H2V17H3V14H4L4 11H5V8H7V4H6V2ZM9 4V8H14.999L15 4H9ZM13 10H11V21H13V10ZM15 21H20V19H19V16H18V13H17V10H15V21ZM9 21V10H7V13H6V16H5L5 19H4V21H9ZM11 4.99805H13.0039V7.00195H11V4.99805Z"}}]},b=v({name:"PyramidMayaIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,a){var{attrs:r}=a,t=n(()=>e.size),{className:o,style:c}=y(t),p=n(()=>["t-icon","t-icon-pyramid-maya",o.value]),H=n(()=>s(s({},c.value),r.style)),u=n(()=>({class:p.value,style:H.value,onClick:V=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:V})}}));return()=>f(m,u.value)}});export{b as default};
