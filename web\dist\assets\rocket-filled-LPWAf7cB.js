import{d as v,D as a,aa as d,ab as O,ac as y}from"./index-CgN1WJ3_.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function c(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M18.0029 13.0675L20.2637 10.8067C21.9515 9.11889 22.8997 6.8297 22.8997 4.44275V1.09961H19.5566C17.1696 1.09961 14.8804 2.04782 13.1926 3.73565L10.9319 5.99642L5.275 5.28931L1.04008 9.52423L14.4751 22.9593L18.71 18.7243L18.0029 13.0675ZM16.2113 14.8591L16.6041 18.0018L14.4751 20.1308L12.7073 18.3631L16.2113 14.8591ZM9.14024 7.78803L5.63627 11.292L3.8685 9.52423L5.99754 7.39519L9.14024 7.78803ZM5.63627 16.9489L2.10074 20.4844L0.686523 19.0702L4.22206 15.5346L5.63627 16.9489ZM8.4647 19.7773L4.92916 23.3128L3.51495 21.8986L7.05048 18.3631L8.4647 19.7773Z"}}]},g=v({name:"RocketFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:s}=d(r),p=a(()=>["t-icon","t-icon-rocket-filled",l.value]),u=a(()=>c(c({},s.value),t.style)),L=a(()=>({class:p.value,style:u.value,onClick:f=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:f})}}));return()=>O(m,L.value)}});export{g as default};
