import{d as O,D as a,aa as y,ab as d,ac as m}from"./index-CgN1WJ3_.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M3.07089 13.9999C3.55612 17.3922 6.47353 19.9999 10 19.9999H18.5V17.9999H10C7.23858 17.9999 5 15.7613 5 12.9999C5 10.2385 7.23858 7.99991 10 7.99991L17.0858 7.99991L14.5858 10.4999L16 11.9141L20.9142 6.99991L16 2.08569L14.5858 3.49991L17.0858 5.99991L10 5.99991C6.13401 5.99991 3 9.13392 3 12.9999L3 13.9999H3.07089Z"}}]},C=O({name:"RollfrontIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=y(t),p=a(()=>["t-icon","t-icon-rollfront",o.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:v})}}));return()=>d(b,f.value)}});export{C as default};
