import{d as C,D as a,aa as d,ab as O,ac as y}from"./index-CgN1WJ3_.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function c(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C15.689 21 18.8618 18.7803 20.2516 15.5996L22.0843 16.4004C20.3878 20.2833 16.5123 23 12 23C5.92487 23 1 18.0751 1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12V14L19.4 11.3L20.6 9.7L20.7282 9.79616C19.7453 5.891 16.2102 3 12 3ZM12 8.5C12.6904 8.5 13.25 9.05964 13.25 9.75V10.5H10.75V9.75C10.75 9.05964 11.3096 8.5 12 8.5ZM15.25 10.5V9.75C15.25 7.95508 13.7949 6.5 12 6.5C10.2051 6.5 8.75 7.95507 8.75 9.75V10.5H7.49854V17H16.4985V10.5H15.25ZM14.4985 12.5V15H9.49854V12.5H14.4985Z"}}]},g=C({name:"RotateLockedIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:s}=d(r),p=a(()=>["t-icon","t-icon-rotate-locked",o.value]),u=a(()=>c(c({},s.value),t.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>O(m,v.value)}});export{g as default};
