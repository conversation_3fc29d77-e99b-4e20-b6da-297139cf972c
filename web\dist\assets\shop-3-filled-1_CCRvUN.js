import{d,D as a,aa as O,ab as y,ac as g}from"./index-CgN1WJ3_.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){g(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var h={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M2 21.9999V8.62515C2.93941 9.33104 4.10759 9.75 5.375 9.75 6.61448 9.75 7.75906 9.34917 8.6875 8.67138 9.61594 9.34917 10.7605 9.75 12 9.75 13.2395 9.75 14.3841 9.34917 15.3125 8.67138 16.2409 9.34917 17.3855 9.75 18.625 9.75 19.8924 9.75 21.0606 9.33104 22 8.62515V21.9999H16.5V13.9999H7.5V21.9999H2zM5.375 7.75C3.8408 7.75 2.52921 6.79692 2 5.45053V2H22V5.45053C21.4708 6.79692 20.1592 7.75 18.625 7.75 17.9214 7.75 17.2646 7.54955 16.7087 7.20263 16.0976 6.82133 15.6083 6.2631 15.3125 5.59962 15.0167 6.2631 14.5274 6.82133 13.9163 7.20263 13.3604 7.54955 12.7036 7.75 12 7.75 11.2964 7.75 10.6396 7.54955 10.0837 7.20263 9.47259 6.82133 8.98331 6.2631 8.6875 5.59962 8.3917 6.2631 7.90241 6.82133 7.29134 7.20263 6.73537 7.54955 6.0786 7.75 5.375 7.75z"}},{tag:"path",attrs:{fill:"currentColor",d:"M9.5 21.9999V15.9999H14.5V21.9999H9.5Z"}}]},b=d({name:"Shop3FilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=O(r),p=a(()=>["t-icon","t-icon-shop-3-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(h,f.value)}});export{b as default};
