import{d,D as a,aa as V,ab as O,ac as y}from"./index-CgN1WJ3_.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var C={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M21 7H22V9H21V20H22V22H16.5V16.5C16.5 14.0147 14.4853 12 12 12C9.51472 12 7.5 14.0147 7.5 16.5V22H2V20H3V9H2V7H3V6C3 3.79086 4.79086 2 7 2H17C19.2091 2 21 3.79086 21 6V7ZM7 4C5.89543 4 5 4.89543 5 6V7H8V4H7ZM10 4V7H14V4H10ZM16 4V7H19V6C19 4.89543 18.1046 4 17 4H16Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M12 14C10.6193 14 9.5 15.1193 9.5 16.5V22H14.5V16.5C14.5 15.1193 13.3807 14 12 14Z"}}]},g=d({name:"Shop4FilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=V(r),p=a(()=>["t-icon","t-icon-shop-4-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>O(C,f.value)}});export{g as default};
