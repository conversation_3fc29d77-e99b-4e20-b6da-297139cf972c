import{d,D as a,aa as O,ab as y,ac as m}from"./index-CgN1WJ3_.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var C={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M14.8091 1.36241L11.4983 7.20403 21.9016 7.20403C20.5176 4.3516 17.9368 2.1862 14.8091 1.36241zM22.6191 9.12211L15.7974 9.12211 20.993 18.3358C22.2569 16.5446 22.9998 14.358 22.9998 12.0002 22.9998 11.0053 22.8674 10.0402 22.6191 9.12211zM19.6664 19.8883L16.3168 13.953 11.2297 22.9737C11.4843 22.9913 11.741 23.0002 11.9998 23.0002 14.9814 23.0002 17.6861 21.8132 19.6664 19.8883zM9.21356 22.6441L12.5151 16.7944H2.09693C3.48413 19.6549 6.07466 21.8248 9.21356 22.6441zM1.37982 14.8763L8.20211 14.8763 3.01563 5.65176C1.74616 7.44533.999756 9.63686.999756 12.0002.999756 12.9945 1.1319 13.9589 1.37982 14.8763zM4.34438 4.10125C6.32369 2.18281 9.02378 1.00024 11.9998 1.00024 12.2666 1.00024 12.5314 1.00977 12.7938 1.02851L7.68635 10.0462 4.34438 4.10125z"}}]},g=d({name:"ShutterFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=O(r),p=a(()=>["t-icon","t-icon-shutter-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(C,f.value)}});export{g as default};
