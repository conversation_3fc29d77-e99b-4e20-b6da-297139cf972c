import{d as L,D as a,aa as O,ab as y,ac as d}from"./index-CgN1WJ3_.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M20.7814 8.13243C22.1186 6.79523 22.1186 4.62721 20.7814 3.29001C19.4442 1.9528 17.2762 1.95281 15.939 3.29002L14.1907 5.03826L12.9051 3.75266L11.4909 5.16687L12.7765 6.45248L2.07227 17.1569L2.07227 21.9993H6.9147L17.619 11.2949L18.9045 12.5805L20.3187 11.1663L19.0332 9.88069L20.7814 8.13243ZM16.2048 9.88069L6.08627 19.9993H4.07226L4.07227 17.9853L14.1908 7.86669L16.2048 9.88069ZM15.605 6.45247L17.3532 4.70423C17.9093 4.14807 18.811 4.14807 19.3672 4.70422C19.9233 5.26037 19.9233 6.16207 19.3672 6.71822L17.619 8.46647L15.605 6.45247Z"}}]},g=L({name:"SipIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=O(t),p=a(()=>["t-icon","t-icon-sip",o.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:f})}}));return()=>y(m,v.value)}});export{g as default};
