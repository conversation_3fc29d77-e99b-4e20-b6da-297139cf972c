import{d,D as a,aa as O,ab as y,ac as m}from"./index-CgN1WJ3_.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M16.5576 12.356L11.7151 7.51354 2.07153 17.1573 2.07153 21.9997 6.91397 21.9997 16.5576 12.356zM20.7803 8.13243C22.1174 6.79523 22.1174 4.62721 20.7802 3.29001 19.443 1.9528 17.275 1.95281 15.9378 3.29002L14.1896 5.03826 12.904 3.75266 11.4898 5.16687 18.9034 12.5805 20.3176 11.1663 19.032 9.88069 20.7803 8.13243z"}}]},P=d({name:"SipFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=O(t),p=a(()=>["t-icon","t-icon-sip-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>y(b,f.value)}});export{P as default};
