import{d as v,D as a,aa as O,ab as y,ac as d}from"./index-CgN1WJ3_.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(L){return Object.getOwnPropertyDescriptor(e,L).enumerable})),r.push.apply(r,t)}return r}function i(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M13 1V2.58579L14 1.58579L15.4142 3L13 5.41421L13 9.61712L16.943 5.6361L18.3639 7.0435L14.4143 11.0313L18.583 11.0028L21 8.58579L22.4142 10L21.4142 11H23V13H21.4142L22.4142 14L21 15.4142L18.5886 13.0028L14.4142 13.0314L18.3639 16.9429L16.9566 18.364L13 14.4456L13 18.5858L15.4142 21L14 22.4142L13 21.4142L13 23L11 23L11 21.4142L10 22.4142L8.58579 21L11 18.5858L11 14.4456L7.04339 18.364L5.63606 16.9429L9.58576 13.0314L5.41138 13.0028L3 15.4142L1.58579 14L2.58579 13L1 13L1 11L2.58578 11L1.58579 10L3 8.58579L5.41704 11.0028L9.58573 11.0313L5.63606 7.0435L7.05705 5.6361L11 9.61712L11 5.41421L8.58579 3L10 1.58579L11 2.58578V1L13 1Z"}}]},g=v({name:"SnowflakeIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:L,style:s}=O(t),c=a(()=>["t-icon","t-icon-snowflake",L.value]),p=a(()=>i(i({},s.value),r.style)),u=a(()=>({class:c.value,style:p.value,onClick:f=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:f})}}));return()=>y(m,u.value)}});export{g as default};
