import{d,D as a,aa as O,ab as y,ac as m}from"./index-CgN1WJ3_.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M5 6.4998V17.4998L1 17.5 1 6.5 5 6.4998zM7 18.2099L15.0004 22.7095 15.0004 1.29004 7 5.7896V18.2099zM19.3889 8.10994L18.6818 7.40283 17.2676 8.81705 17.9747 9.52415C19.3415 10.891 19.3415 13.1071 17.9747 14.4739L17.2676 15.181 18.6818 16.5952 19.3889 15.8881C21.5368 13.7402 21.5368 10.2578 19.3889 8.10994z"}}]},w=d({name:"SoundLowFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=O(t),p=a(()=>["t-icon","t-icon-sound-low-filled",o.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:v})}}));return()=>y(b,f.value)}});export{w as default};
