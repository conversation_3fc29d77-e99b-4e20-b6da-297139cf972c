import{d as O,D as a,aa as d,ab as y,ac as m}from"./index-CgN1WJ3_.js";function s(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function i(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?s(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):s(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M10 1H14V5H10V1ZM2 6H22V8.6594L15 11.6594V17H15.7808L17.2808 23H6.71922L8.21922 17H9V11.6594L2 8.6594V6ZM9 9.48346V8H5.53859L9 9.48346ZM11 8V10.5858L13 12.5858V8H11ZM15 8V9.48346L18.4614 8H15ZM13 15.4142L11 13.4142V17H13V15.4142Z"}}]},V=O({name:"StatueOfJesusFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=d(r),u=a(()=>["t-icon","t-icon-statue-of-jesus-filled",l.value]),p=a(()=>i(i({},c.value),t.style)),f=a(()=>({class:u.value,style:p.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(b,f.value)}});export{V as default};
