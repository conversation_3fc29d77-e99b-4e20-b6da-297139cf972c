import{d as y,D as a,aa as O,ab as m,ac as g}from"./index-CgN1WJ3_.js";function l(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function i(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?l(Object(t),!0).forEach(function(r){g(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var d={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M8.5 4.00041V7.62538H11V4.00022L8.5 4.00041ZM13 4.00024V9.62538H6.5V4.00049H4V20.0001H20V9.0396L14.9607 4.00045L13 4.00024ZM15.7893 2.00053L22 8.21116V22.0001H2V2.00049L15.7893 2.00053ZM7 14.0001V12.0001H17V14.0001H7ZM7 17.0001V15.0001H13V17.0001H7Z"}}]},V=y({name:"SystemStorageIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:c}=O(r),p=a(()=>["t-icon","t-icon-system-storage",o.value]),u=a(()=>i(i({},c.value),t.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var s;return(s=e.onClick)===null||s===void 0?void 0:s.call(e,{e:f})}}));return()=>m(d,v.value)}});export{V as default};
