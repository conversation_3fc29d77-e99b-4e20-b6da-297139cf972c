import{d as v,D as a,aa as O,ab as y,ac as g}from"./index-CgN1WJ3_.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function c(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){g(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var h={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M16 1H8V5H16V1Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M6 3H3V23H12.8762C12.0139 21.897 11.5 20.5085 11.5 19C11.5 15.4102 14.4101 12.5 18 12.5C19.0821 12.5 20.1025 12.7644 21 13.2322V3H18V7H6V3Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M12.3438 19.4143L13.758 18.0001L16.5864 20.8285L22.2432 15.1716L23.6575 16.5858L16.5864 23.6569L12.3438 19.4143Z"}}]},m=v({name:"TaskCheckedFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:s}=O(r),p=a(()=>["t-icon","t-icon-task-checked-filled",l.value]),u=a(()=>c(c({},s.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:d=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:d})}}));return()=>y(h,f.value)}});export{m as default};
