import{d as f,D as a,aa as d,ab as O,ac as y}from"./index-CgN1WJ3_.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function c(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M23 4.5C23 6.433 21.433 8 19.5 8C17.9145 8 16.5752 6.94574 16.1449 5.5H14C13.4478 5.5 13 5.94772 13 6.5V17.5C13 18.5042 13.4078 18.5025 13.9251 18.5002L14 18.5H16.1449C16.5752 17.0543 17.9145 16 19.5 16C21.433 16 23 17.567 23 19.5C23 21.433 21.433 23 19.5 23C17.9145 23 16.5752 21.9457 16.1449 20.5H14C12.3432 20.5 11 19.1569 11 17.5V13L7.85506 13C7.42479 14.4457 6.08551 15.5 4.5 15.5C2.567 15.5 1 13.933 1 12C1 10.067 2.567 8.5 4.5 8.5C6.08551 8.5 7.42479 9.55426 7.85506 11L11 11V6.5C11 4.84315 12.3432 3.5 14 3.5H16.1449C16.5752 2.05426 17.9145 1 19.5 1C21.433 1 23 2.567 23 4.5Z"}}]},g=f({name:"TreeRoundDotVerticalFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:s}=d(r),p=a(()=>["t-icon","t-icon-tree-round-dot-vertical-filled",l.value]),u=a(()=>c(c({},s.value),t.style)),v=a(()=>({class:p.value,style:u.value,onClick:C=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:C})}}));return()=>O(m,v.value)}});export{g as default};
