import{d as v,D as a,aa as d,ab as O,ac as m}from"./index-CgN1WJ3_.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12ZM14.501 15.5C15.0533 15.5 15.501 15.9477 15.501 16.5V17.5H17.501V16.5C17.501 14.8431 16.1578 13.5 14.501 13.5C13.7016 13.5 12.9734 13.814 12.4365 14.3233C12.3501 14.4053 12.2803 14.4548 12.2299 14.4813C12.2058 14.4939 12.1916 14.4985 12.186 14.5H11.816C11.8103 14.4985 11.7961 14.4939 11.7721 14.4813C11.7217 14.4548 11.6519 14.4053 11.5655 14.3233C11.0286 13.814 10.3004 13.5 9.50098 13.5C7.84411 13.5 6.50098 14.8431 6.50098 16.5V17.5H8.50098V16.5C8.50098 15.9477 8.94868 15.5 9.50098 15.5C9.76805 15.5 10.0088 15.6034 10.189 15.7743C10.5113 16.08 11.0674 16.5 11.8102 16.5H12.1917C12.9346 16.5 13.4907 16.08 13.813 15.7743C13.9931 15.6034 14.2339 15.5 14.501 15.5ZM6.76855 11.8658L10.2327 9.86584L9.23266 8.13379L5.76855 10.1338L6.76855 11.8658ZM18.2327 10.1338L14.7686 8.13379L13.7686 9.86584L17.2327 11.8658L18.2327 10.1338Z"}}]},g=v({name:"UncomfortableFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:s}=d(t),p=a(()=>["t-icon","t-icon-uncomfortable-filled",l.value]),u=a(()=>c(c({},s.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:C=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:C})}}));return()=>O(y,f.value)}});export{g as default};
