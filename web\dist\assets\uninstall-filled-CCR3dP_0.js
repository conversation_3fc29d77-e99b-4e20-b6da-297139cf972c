import{d,D as a,aa as O,ab as y,ac as g}from"./index-CgN1WJ3_.js";function o(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?o(Object(t),!0).forEach(function(r){g(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):o(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M16.9142 6.45208L12 1.59375L7.08581 6.45207L8.49191 7.87435L11 5.39477V12.504H13V5.39478L15.5081 7.87435L16.9142 6.45208Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M7.5 2.00003H2V22H22V2.00003H16.5V4.00003H20V14H4V4.00003H7.5V2.00003ZM8.00195 16.998V19.002H5.99805V16.998H8.00195ZM11.002 16.998V19.002H8.99805V16.998H11.002Z"}}]},V=d({name:"UninstallFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=O(r),p=a(()=>["t-icon","t-icon-uninstall-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>y(m,f.value)}});export{V as default};
