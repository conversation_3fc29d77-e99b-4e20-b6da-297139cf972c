import{d as C,D as a,aa as d,ab as O,ac as y}from"./index-CgN1WJ3_.js";function o(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?o(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):o(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var g={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M11.5 2C8.46243 2 6 4.46243 6 7.5 6 10.5376 8.46243 13 11.5 13 14.5376 13 17 10.5376 17 7.5 17 4.46243 14.5376 2 11.5 2zM19.4998 12.75V14.126C20.2147 14.31 20.8523 14.6867 21.3539 15.1975L22.5464 14.509 23.5464 16.241 22.3548 16.929C22.4493 17.2699 22.4998 17.629 22.4998 18 22.4998 18.371 22.4493 18.7301 22.3548 19.071L23.5464 19.759 22.5464 21.491 21.3539 20.8025C20.8523 21.3133 20.2147 21.69 19.4998 21.874V23.25H17.4998V21.874C16.7848 21.69 16.1472 21.3133 15.6457 20.8025L14.4531 21.491 13.4531 19.759 14.6447 19.071C14.5503 18.7301 14.4998 18.371 14.4998 18 14.4998 17.629 14.5503 17.2699 14.6447 16.929L13.4531 16.241 14.4531 14.509 15.6457 15.1975C16.1472 14.6867 16.7848 14.31 17.4998 14.126V12.75H19.4998zM16.7485 17.0333C16.59 17.3198 16.4998 17.6494 16.4998 18 16.4998 18.3506 16.59 18.6802 16.7485 18.9667L16.7851 19.03C17.1349 19.6112 17.7719 20 18.4998 20 19.2276 20 19.8646 19.6112 20.2145 19.03L20.251 18.9668C20.4095 18.6802 20.4998 18.3507 20.4998 18 20.4998 17.6493 20.4095 17.3198 20.251 17.0332L20.2145 16.97C19.8646 16.3888 19.2276 16 18.4998 16 17.7719 16 17.1349 16.3888 16.785 16.97L16.7485 17.0333zM13.0623 14C12.2375 15.1195 11.75 16.5028 11.75 18 11.75 19.4972 12.2375 20.8805 13.0623 22H2V20C2 16.6863 4.68629 14 8 14H13.0623z"}}]},b=C({name:"UserSettingFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=d(r),p=a(()=>["t-icon","t-icon-user-setting-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>O(g,f.value)}});export{b as default};
