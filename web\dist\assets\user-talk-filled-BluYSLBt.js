import{d,D as a,aa as O,ab as y,ac as C}from"./index-CgN1WJ3_.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){C(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var g={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M11.5 2C8.46243 2 6 4.46243 6 7.5 6 10.5376 8.46243 13 11.5 13 14.5376 13 17 10.5376 17 7.5 17 4.46243 14.5376 2 11.5 2zM18.9837 12.9819L19.5863 13.7799C20.5044 14.9956 21.0008 16.4776 21.0003 18.0009 20.9998 19.5243 20.5023 21.0059 19.5834 22.221L18.9802 23.0185 17.385 21.8121 17.9882 21.0145C18.6446 20.1467 18.9999 19.0884 19.0003 18.0002 19.0007 16.9121 18.6461 15.8536 17.9903 14.9852L17.3877 14.1872 18.9837 12.9819z"}},{tag:"path",attrs:{fill:"currentColor",d:"M16.1909 15.0911L16.7936 15.8891C17.2526 16.4969 17.5008 17.2379 17.5006 17.9996 17.5003 18.7613 17.2516 19.5021 16.7921 20.1096L16.1889 20.9072 14.5938 19.7007 15.197 18.9032C15.3939 18.6428 15.5005 18.3253 15.5006 17.9989 15.5007 17.6724 15.3943 17.3549 15.1976 17.0944L14.5949 16.2964 16.1909 15.0911zM12.8762 14C12.0139 15.103 11.5 16.4915 11.5 18 11.5 19.5085 12.0139 20.897 12.8762 22H2V20C2 16.6863 4.68629 14 8 14H12.8762z"}}]},b=d({name:"UserTalkFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=O(t),p=a(()=>["t-icon","t-icon-user-talk-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(g,f.value)}});export{b as default};
