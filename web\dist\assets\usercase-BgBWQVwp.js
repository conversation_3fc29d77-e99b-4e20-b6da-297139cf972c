import{d as f,D as a,aa as O,ab as y,ac as d}from"./index-CgN1WJ3_.js";function s(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(C){return Object.getOwnPropertyDescriptor(e,C).enumerable})),r.push.apply(r,t)}return r}function l(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?s(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M18 7C17.3858 7 16.8219 7.15883 16.4334 7.41784C16.0529 7.67146 16 7.90148 16 8C16 8.09852 16.0529 8.32854 16.4334 8.58216C16.8219 8.84117 17.3858 9 18 9C18.6141 9 19.1781 8.84117 19.5666 8.58216C19.947 8.32854 20 8.09852 20 8C20 7.90148 19.947 7.67146 19.5666 7.41784C19.1781 7.15883 18.6141 7 18 7ZM18 5C18.9214 5 19.8929 5.23169 20.676 5.75374C21.4671 6.28116 22 7.07483 22 8C22 8.92517 21.4671 9.71884 20.676 10.2463C19.8929 10.7683 18.9214 11 18 11C17.0786 11 16.107 10.7683 15.324 10.2463C14.5328 9.71884 14 8.92517 14 8C14 7.07483 14.5328 6.28116 15.324 5.75374C16.107 5.23169 17.0786 5 18 5ZM5.70709 7.29289C6.09762 7.68342 6.09762 8.31658 5.70709 8.70711C5.51221 8.902 5.25689 8.99963 5.00146 9C5.00048 9 4.9995 9 4.99852 9C4.74309 8.99963 4.48777 8.902 4.29288 8.70711C3.90236 8.31658 3.90236 7.68342 4.29288 7.29289C4.68341 6.90237 5.31657 6.90237 5.70709 7.29289ZM5.99999 10.8292C6.40944 10.6848 6.79381 10.4488 7.12131 10.1213C8.29288 8.94975 8.29288 7.05025 7.12131 5.87868C5.94974 4.70711 4.05024 4.70711 2.87867 5.87868C1.70709 7.05025 1.70709 8.94975 2.87867 10.1213C3.20616 10.4488 3.59053 10.6848 3.99999 10.8292V12H1.99999V14H3.99999V15.5857L1.58594 17.9986L2.99984 19.4132L5.00035 17.4135L7.00291 19.4132L8.41609 17.9979L5.99999 15.5854V14H7.99999V12H5.99999V10.8292ZM9.49999 7H12.5V9H9.49999V7ZM18 15C17.3858 15 16.8219 15.1588 16.4334 15.4178C16.0529 15.6715 16 15.9015 16 16C16 16.0985 16.0529 16.3285 16.4334 16.5822C16.8219 16.8412 17.3858 17 18 17C18.6141 17 19.1781 16.8412 19.5666 16.5822C19.947 16.3285 20 16.0985 20 16C20 15.9015 19.947 15.6715 19.5666 15.4178C19.1781 15.1588 18.6141 15 18 15ZM18 13C18.9214 13 19.8929 13.2317 20.676 13.7537C21.4671 14.2812 22 15.0748 22 16C22 16.9252 21.4671 17.7188 20.676 18.2463C19.8929 18.7683 18.9214 19 18 19C17.0786 19 16.107 18.7683 15.324 18.2463C14.5328 17.7188 14 16.9252 14 16C14 15.0748 14.5328 14.2812 15.324 13.7537C16.107 13.2317 17.0786 13 18 13ZM9.49999 15H12.5V17H9.49999V15Z"}}]},g=f({name:"UsercaseIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:C,style:i}=O(t),c=a(()=>["t-icon","t-icon-usercase",C.value]),p=a(()=>l(l({},i.value),r.style)),u=a(()=>({class:c.value,style:p.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(m,u.value)}});export{g as default};
