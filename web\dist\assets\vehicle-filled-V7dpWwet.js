import{d,D as a,aa as H,ab as O,ac as y}from"./index-CgN1WJ3_.js";function o(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function c(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?o(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):o(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var V={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M2 3C0.895431 3 0 3.89543 0 5V18C0 19.1046 0.89543 20 2 20H2.58579L2.08579 20.5L3.5 21.9142L5.41421 20H8V12H2V5H13V7H15V5C15 3.89543 14.1046 3 13 3H2ZM3 15H5.00391V17.0039H3V15Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M21.677 8H11.323L9 13.8074V21.5H11V20H22V21.5H24V13.8074L21.677 8ZM21.123 12H11.877L12.677 10H20.323L21.123 12ZM12 15H14.0039V17.0039H12V15ZM21.0039 15V17.0039H19V15H21.0039Z"}}]},h=d({name:"VehicleFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:s}=H(r),p=a(()=>["t-icon","t-icon-vehicle-filled",l.value]),u=a(()=>c(c({},s.value),t.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:f})}}));return()=>O(V,v.value)}});export{h as default};
