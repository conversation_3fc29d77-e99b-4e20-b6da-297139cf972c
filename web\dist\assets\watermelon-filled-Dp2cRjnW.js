import{d as C,D as a,aa as d,ab as O,ac as m}from"./index-CgN1WJ3_.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var y={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M17.5006 1.08594L18.2077 1.79304C20.0103 3.59563 20.5457 6.52564 20.5006 8.99865L20.5006 9.00139C20.4936 15.3483 15.3501 20.4923 9.00341 20.5001C6.52295 20.552 3.83978 19.8266 1.88216 18.286L0.99707 17.5895L17.5006 1.08594ZM16.5023 8.49805H14.4984V10.502H16.5023V8.49805ZM13.5023 11.498H11.4984V13.502H13.5023V11.498ZM10.5023 14.498H8.49835V16.502H10.5023V14.498Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M21.4997 9.49986C21.4997 16.1273 16.1271 21.4999 9.49968 21.4999C7.90658 21.4999 6.386 21.1894 4.99512 20.6257C5.29501 20.799 5.60178 20.9644 5.91307 21.12C7.50687 21.9163 9.31045 22.4998 10.9883 22.4998C17.3461 22.4998 22.5001 17.3458 22.5001 10.988C22.5001 9.31017 21.9166 7.50658 21.1203 5.91278C20.964 5.60007 20.7978 5.29192 20.6237 4.99072C21.1886 6.38285 21.4997 7.90502 21.4997 9.49986Z"}}]},b=C({name:"WatermelonFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=d(r),p=a(()=>["t-icon","t-icon-watermelon-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>O(y,f.value)}});export{b as default};
