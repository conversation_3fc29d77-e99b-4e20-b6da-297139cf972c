import{d as y,D as a,aa as C,ab as d,ac as O}from"./index-CgN1WJ3_.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){O(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M13 5C12.4477 5 12 5.44772 12 6C12 6.55228 12.4477 7 13 7H22V9H13C11.3431 9 10 7.65685 10 6C10 4.34315 11.3431 3 13 3H17V5H13ZM5.5 8C4.67157 8 4 8.67157 4 9.5C4 10.3284 4.67157 11 5.5 11H22V13H5.5C3.567 13 2 11.433 2 9.5C2 7.567 3.567 6 5.5 6H8V8H5.5ZM5 18C5 16.3431 6.34315 15 8 15H18V17H8C7.44772 17 7 17.4477 7 18C7 18.5523 7.44772 19 8 19H12.5V21H8C6.34315 21 5 19.6569 5 18Z"}}]},g=y({name:"WindyIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=C(t),p=a(()=>["t-icon","t-icon-windy",o.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:f})}}));return()=>d(m,v.value)}});export{g as default};
